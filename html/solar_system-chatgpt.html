<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>太阳系运行图</title>
    <style>
        /* 设置页面无滚动、全屏黑色背景 */
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
        }

        /* 控制面板样式 */
        #controlPanel {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            font-family: sans-serif;
            color: #fff;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
        }

        #controlPanel button {
            margin: 5px;
            padding: 5px 10px;
            cursor: pointer;
            background: #333;
            border: none;
            color: #fff;
            border-radius: 3px;
        }

        #controlPanel button:hover {
            background: #555;
        }
    </style>
</head>

<body>
    <!-- 控制动画速度的面板 -->
    <div id="controlPanel">
        <button id="slowBtn">慢速</button>
        <button id="mediumBtn">中速</button>
        <button id="fastBtn">快速</button>
    </div>

    <!-- 引入 Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 全局变量：动画运行速度因子（默认为1）
        let simulationSpeed = 1;

        // 设置按钮事件，调整 simulationSpeed 参数
        document.getElementById('slowBtn').addEventListener('click', () => {
            simulationSpeed = 0.5;
        });
        document.getElementById('mediumBtn').addEventListener('click', () => {
            simulationSpeed = 1;
        });
        document.getElementById('fastBtn').addEventListener('click', () => {
            simulationSpeed = 2;
        });

        // 创建场景、摄像机和渲染器
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 20, 40);
        camera.lookAt(scene.position);

        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);

        // 添加环境光和点光源（太阳光）
        const ambientLight = new THREE.AmbientLight(0x333333);
        scene.add(ambientLight);

        const pointLight = new THREE.PointLight(0xffffff, 2, 300);
        pointLight.position.set(0, 0, 0);
        scene.add(pointLight);

        // 创建太阳——位于中心的发光体
        const sunGeometry = new THREE.SphereGeometry(3, 32, 32);
        // 使用 MeshBasicMaterial 保持自发光效果
        const sunMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
        const sun = new THREE.Mesh(sunGeometry, sunMaterial);
        scene.add(sun);

        // 给太阳添加一个外发光效果（使用一个稍大、半透明、反向渲染的球体）
        const sunGlowGeometry = new THREE.SphereGeometry(3.5, 32, 32);
        const sunGlowMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffaa,
            transparent: true,
            opacity: 0.5,
            blending: THREE.AdditiveBlending,
            side: THREE.BackSide
        });
        const sunGlow = new THREE.Mesh(sunGlowGeometry, sunGlowMaterial);
        sun.add(sunGlow);

        // 定义各行星的参数：名称、半径、轨道距离、颜色、运动速度
        const planets = [
            { name: 'Mercury', radius: 0.3, distance: 5, color: 0x909090, speed: 0.04 },
            { name: 'Venus', radius: 0.5, distance: 7, color: 0xFFFACD, speed: 0.035 },
            { name: 'Earth', radius: 0.5, distance: 9, color: 0x20B2AA, speed: 0.03, hasMoon: true },
            { name: 'Mars', radius: 0.4, distance: 11, color: 0xFF4500, speed: 0.025 },
            { name: 'Jupiter', radius: 1.0, distance: 14, color: 0xD2B48C, speed: 0.02 },
            { name: 'Saturn', radius: 0.9, distance: 17, color: 0xDAA520, speed: 0.018, hasRing: true },
            { name: 'Uranus', radius: 0.7, distance: 20, color: 0x66CDAA, speed: 0.015 },
            { name: 'Neptune', radius: 0.7, distance: 23, color: 0x00008B, speed: 0.012 },
            { name: 'Pluto', radius: 0.2, distance: 26, color: 0x8B7D6B, speed: 0.01 }
        ];

        // 用于存储每个行星的父容器（旋转节点）及相关信息
        const planetSystems = [];

        // 辅助函数：创建轨道线（圆形）
        function createOrbitLine(radius) {
            const segments = 64;
            const points = [];
            for (let i = 0; i <= segments; i++) {
                const theta = (i / segments) * Math.PI * 2;
                points.push(new THREE.Vector3(Math.cos(theta) * radius, 0, Math.sin(theta) * radius));
            }
            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const material = new THREE.LineBasicMaterial({ color: 0x444444 });
            return new THREE.LineLoop(geometry, material);
        }

        // 创建每个行星及其轨道（同时添加轨道线）
        planets.forEach(planet => {
            // 每个行星的旋转容器，用于模拟绕太阳运行
            const pivot = new THREE.Object3D();
            scene.add(pivot);

            // 添加轨道线（可选）
            const orbitLine = createOrbitLine(planet.distance);
            scene.add(orbitLine);

            // 创建行星球体
            const geometry = new THREE.SphereGeometry(planet.radius, 32, 32);
            const material = new THREE.MeshStandardMaterial({ color: planet.color });
            const mesh = new THREE.Mesh(geometry, material);
            // 将行星放置在 x 轴正方向（距离为 planet.distance）
            mesh.position.x = planet.distance;
            pivot.add(mesh);

            // 保存旋转容器及运动速度
            const system = { pivot: pivot, speed: planet.speed };

            // 若该行星有卫星（如地球的月球），则创建一个子容器模拟月球运行
            if (planet.hasMoon) {
                const moonPivot = new THREE.Object3D();
                mesh.add(moonPivot);
                const moonGeometry = new THREE.SphereGeometry(0.1, 16, 16);
                const moonMaterial = new THREE.MeshStandardMaterial({ color: 0x888888 });
                const moon = new THREE.Mesh(moonGeometry, moonMaterial);
                moon.position.x = 1.2; // 卫星距离地球的距离
                moonPivot.add(moon);
                system.moonPivot = moonPivot;
                system.moonSpeed = 0.05; // 月球运行速度
            }

            // 若为土星，则添加环带效果
            if (planet.hasRing) {
                // 创建环形几何体（内外半径可以根据行星半径调整）
                const ringGeometry = new THREE.RingGeometry(planet.radius * 1.2, planet.radius * 2, 32);
                const ringMaterial = new THREE.MeshBasicMaterial({
                    color: 0xC2B280,
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.7
                });
                const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                // 将环带倾斜，使之平行于行星轨道平面
                ring.rotation.x = Math.PI / 2;
                mesh.add(ring);
            }

            planetSystems.push(system);
        });

        // 创建星空背景：使用随机分布的小白点模拟星星
        function createStarField() {
            const starCount = 1000;
            const starsGeometry = new THREE.BufferGeometry();
            const positions = [];
            for (let i = 0; i < starCount; i++) {
                const x = (Math.random() - 0.5) * 500;
                const y = (Math.random() - 0.5) * 500;
                const z = (Math.random() - 0.5) * 500;
                positions.push(x, y, z);
            }
            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
            const starsMaterial = new THREE.PointsMaterial({ color: 0xffffff });
            const starField = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(starField);
        }
        createStarField();

        // 时钟用于控制动画时间
        const clock = new THREE.Clock();

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            const delta = clock.getDelta();
            const elapsed = clock.getElapsedTime();

            // 太阳脉动效果：根据正弦函数微调太阳的缩放
            const scale = 1 + 0.1 * Math.sin(elapsed * 2);
            sun.scale.set(scale, scale, scale);

            // 更新各行星的旋转容器，实现绕太阳运行
            planetSystems.forEach(system => {
                system.pivot.rotation.y += system.speed * simulationSpeed;
                // 若存在月球，则更新月球绕行
                if (system.moonPivot) {
                    system.moonPivot.rotation.y += system.moonSpeed * simulationSpeed;
                }
            });

            renderer.render(scene, camera);
        }
        animate();

        // 监听窗口大小变化，更新摄像机与渲染器尺寸
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>

</html>