<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态太阳系模拟器</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
            font-family: Arial, sans-serif;
        }

        canvas {
            display: block;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }

        button {
            background-color: #333;
            color: white;
            border: 1px solid #666;
            padding: 5px 10px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background-color: #555;
        }

        .active {
            background-color: #0077ff;
        }

        label {
            display: inline-block;
            margin-right: 10px;
        }

        input[type="range"] {
            vertical-align: middle;
        }
    </style>
</head>

<body>
    <div id="controls">
        <h3>控制面板</h3>
        <div>
            <button id="slow">慢速</button>
            <button id="medium" class="active">中速</button>
            <button id="fast">快速</button>
        </div>
        <div>
            <label>
                <input type="checkbox" id="showLabels" checked> 显示标签
            </label>
            <label>
                <input type="checkbox" id="showOrbits" checked> 显示轨道
            </label>
        </div>
        <div>
            <label>
                缩放: <input type="range" id="zoom" min="1" max="10" value="5" step="0.1">
            </label>
        </div>
    </div>
    <div id="info">
        太阳系模拟器 - 使用鼠标拖动可以旋转视角，滚轮可以缩放
    </div>

    <!-- 使用可靠的CDN资源 -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/geometries/TextGeometry.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/FontLoader.js"></script>

    <script>
        // 初始化Three.js场景
        const scene = new THREE.Scene();

        // 设置相机
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 50, 100);

        // 创建渲染器
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);

        // 添加轨道控制器
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        // 添加环境光和太阳光
        const ambientLight = new THREE.AmbientLight(0x333333);
        scene.add(ambientLight);

        // 创建星星背景
        function createStars() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({
                color: 0xFFFFFF,
                size: 0.1,
                transparent: true
            });

            const starsVertices = [];
            for (let i = 0; i < 5000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starsVertices.push(x, y, z);
            }

            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const stars = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(stars);
        }

        createStars();

        // 天体参数
        const planets = [
            { name: "太阳", color: 0xFDB813, radius: 6, distance: 0, orbitSpeed: 0, rotationSpeed: 0.005 },
            { name: "水星", color: 0xA9A9A9, radius: 0.4, distance: 15, orbitSpeed: 0.04, rotationSpeed: 0.004 },
            { name: "金星", color: 0xE6E6FA, radius: 0.6, distance: 20, orbitSpeed: 0.015, rotationSpeed: 0.002 },
            { name: "地球", color: 0x1E90FF, radius: 0.6, distance: 28, orbitSpeed: 0.01, rotationSpeed: 0.02 },
            { name: "火星", color: 0xC1440E, radius: 0.5, distance: 36, orbitSpeed: 0.008, rotationSpeed: 0.018 },
            { name: "木星", color: 0xDAA06D, radius: 1.3, distance: 50, orbitSpeed: 0.002, rotationSpeed: 0.04 },
            { name: "土星", color: 0xE5D7BD, radius: 1.1, distance: 65, orbitSpeed: 0.0009, rotationSpeed: 0.038 },
            { name: "天王星", color: 0xAFEEEE, radius: 0.9, distance: 80, orbitSpeed: 0.0004, rotationSpeed: 0.03 },
            { name: "海王星", color: 0x4169E1, radius: 0.8, distance: 90, orbitSpeed: 0.0001, rotationSpeed: 0.032 },
            { name: "冥王星", color: 0xA9A9A9, radius: 0.2, distance: 100, orbitSpeed: 0.00007, rotationSpeed: 0.008 }
        ];

        // 创建太阳和行星
        const celestialBodies = [];
        const orbits = [];
        const labels = [];
        let speedMultiplier = 1;
        let showLabels = true;
        let showOrbits = true;
        let zoomLevel = 5;

        // 创建太阳发光效果
        const sunGlow = new THREE.Mesh(
            new THREE.SphereGeometry(7, 32, 32),
            new THREE.MeshBasicMaterial({
                color: 0xFDB813,
                transparent: true,
                opacity: 0.3
            })
        );
        scene.add(sunGlow);

        // 创建土星环
        function createSaturnRings() {
            const ringGeometry = new THREE.RingGeometry(1.5, 2.5, 32);
            const ringMaterial = new THREE.MeshBasicMaterial({
                color: 0xE5D7BD,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.8
            });
            const rings = new THREE.Mesh(ringGeometry, ringMaterial);
            rings.rotation.x = Math.PI / 3;
            return rings;
        }

        // 创建轨道线
        function createOrbit(radius) {
            const orbitGeometry = new THREE.BufferGeometry();
            const points = [];
            const segments = 128;

            for (let i = 0; i <= segments; i++) {
                const theta = (i / segments) * Math.PI * 2;
                points.push(new THREE.Vector3(
                    radius * Math.cos(theta),
                    0,
                    radius * Math.sin(theta)
                ));
            }

            orbitGeometry.setFromPoints(points);
            const orbitMaterial = new THREE.LineBasicMaterial({
                color: 0x444444,
                transparent: true,
                opacity: 0.5
            });
            return new THREE.Line(orbitGeometry, orbitMaterial);
        }

        // 创建行星标签
        function createLabel(text, position) {
            const canvas = document.createElement('canvas');
            canvas.width = 128;
            canvas.height = 64;
            const context = canvas.getContext('2d');
            context.fillStyle = 'rgba(0, 0, 0, 0.5)';
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.font = '24px Arial';
            context.fillStyle = 'white';
            context.textAlign = 'center';
            context.fillText(text, canvas.width / 2, canvas.height / 2 + 8);

            const texture = new THREE.CanvasTexture(canvas);
            const material = new THREE.SpriteMaterial({ map: texture });
            const sprite = new THREE.Sprite(material);
            sprite.scale.set(10, 5, 1);
            sprite.position.copy(position);
            sprite.position.y += 5;
            return sprite;
        }

        // 创建所有天体
        planets.forEach((planet, index) => {
            // 创建行星
            const geometry = new THREE.SphereGeometry(planet.radius, 32, 32);
            let material;

            if (index === 0) { // 太阳
                material = new THREE.MeshBasicMaterial({
                    color: planet.color,
                    emissive: planet.color,
                    emissiveIntensity: 1
                });
            } else {
                material = new THREE.MeshPhongMaterial({
                    color: planet.color,
                    shininess: 10
                });
            }

            const body = new THREE.Mesh(geometry, material);
            body.position.x = planet.distance;

            // 如果是土星，添加环
            if (planet.name === "土星") {
                const rings = createSaturnRings();
                body.add(rings);
            }

            scene.add(body);
            celestialBodies.push({
                mesh: body,
                distance: planet.distance,
                orbitSpeed: planet.orbitSpeed,
                rotationSpeed: planet.rotationSpeed,
                angle: Math.random() * Math.PI * 2
            });

            // 创建轨道线
            if (index > 0) {
                const orbit = createOrbit(planet.distance);
                scene.add(orbit);
                orbits.push(orbit);
            }

            // 创建标签
            if (index === 0) { // 太阳标签放在上方
                const label = createLabel(planet.name, new THREE.Vector3(0, planet.radius + 2, 0));
                body.add(label);
                labels.push(label);
            } else {
                const label = createLabel(planet.name, new THREE.Vector3(planet.distance, 0, 0));
                scene.add(label);
                labels.push(label);
            }

            // 为地球添加月球
            if (planet.name === "地球") {
                const moonGeometry = new THREE.SphereGeometry(0.2, 16, 16);
                const moonMaterial = new THREE.MeshPhongMaterial({ color: 0xAAAAAA });
                const moon = new THREE.Mesh(moonGeometry, moonMaterial);
                moon.position.x = 1.5;

                celestialBodies.push({
                    mesh: moon,
                    distance: 1.5,
                    orbitSpeed: 0.05,
                    rotationSpeed: 0.01,
                    angle: Math.random() * Math.PI * 2,
                    parent: body
                });

                const moonLabel = createLabel("月球", new THREE.Vector3(1.5, 0.2, 0));
                moonLabel.scale.set(5, 2.5, 1);
                body.add(moonLabel);
                labels.push(moonLabel);
            }
        });

        // 太阳脉动动画
        function animateSunPulse() {
            const pulseSpeed = 0.01;
            const minScale = 0.98;
            const maxScale = 1.02;

            const sun = celestialBodies[0].mesh;
            const scale = minScale + (Math.sin(Date.now() * pulseSpeed) + 1) / 2 * (maxScale - minScale);
            sun.scale.set(scale, scale, scale);
            sunGlow.scale.set(scale * 1.1, scale * 1.1, scale * 1.1);
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 更新控制器
            controls.update();

            // 太阳脉动效果
            animateSunPulse();

            // 更新行星位置
            celestialBodies.forEach(body => {
                if (body.orbitSpeed > 0) {
                    body.angle += body.orbitSpeed * speedMultiplier;
                    const parentPosition = body.parent ? body.parent.position : new THREE.Vector3(0, 0, 0);

                    body.mesh.position.x = parentPosition.x + Math.cos(body.angle) * body.distance;
                    body.mesh.position.z = parentPosition.z + Math.sin(body.angle) * body.distance;

                    // 更新标签位置
                    if (labels[celestialBodies.indexOf(body)]) {
                        const label = labels[celestialBodies.indexOf(body)];
                        label.position.copy(body.mesh.position);
                        if (!body.parent) {
                            label.position.y = 0;
                        }
                        label.position.y += 5;
                    }
                }

                // 行星自转
                body.mesh.rotation.y += body.rotationSpeed * speedMultiplier;
            });

            // 更新相机位置基于缩放级别
            camera.position.z = 100 / zoomLevel;

            // 渲染场景
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 控制面板事件
        document.getElementById('slow').addEventListener('click', () => {
            speedMultiplier = 0.5;
            updateSpeedButtons();
        });

        document.getElementById('medium').addEventListener('click', () => {
            speedMultiplier = 1;
            updateSpeedButtons();
        });

        document.getElementById('fast').addEventListener('click', () => {
            speedMultiplier = 2;
            updateSpeedButtons();
        });

        document.getElementById('showLabels').addEventListener('change', (e) => {
            showLabels = e.target.checked;
            labels.forEach(label => label.visible = showLabels);
        });

        document.getElementById('showOrbits').addEventListener('change', (e) => {
            showOrbits = e.target.checked;
            orbits.forEach(orbit => orbit.visible = showOrbits);
        });

        document.getElementById('zoom').addEventListener('input', (e) => {
            zoomLevel = parseFloat(e.target.value);
        });

        function updateSpeedButtons() {
            document.getElementById('slow').classList.remove('active');
            document.getElementById('medium').classList.remove('active');
            document.getElementById('fast').classList.remove('active');

            if (speedMultiplier === 0.5) {
                document.getElementById('slow').classList.add('active');
            } else if (speedMultiplier === 1) {
                document.getElementById('medium').classList.add('active');
            } else {
                document.getElementById('fast').classList.add('active');
            }
        }

        // 开始动画
        animate();
    </script>
</body>

</html>