<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>像素弹球大师</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: 'Courier New', monospace;
            color: #fff;
            overflow: hidden;
            touch-action: manipulation;
        }

        #game-container {
            position: relative;
            width: 512px;
            height: 480px;
            image-rendering: pixelated;
            background-color: #000;
            border: 8px solid #555;
            box-shadow: 0 0 0 4px #333, 0 0 20px rgba(0, 0, 0, 0.5);
        }

        #game-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        #ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .ui-panel {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid #555;
            padding: 10px;
            color: white;
            font-size: 14px;
        }

        #score-panel {
            top: 10px;
            left: 10px;
        }

        #lives-panel {
            top: 10px;
            right: 10px;
        }

        #level-panel {
            bottom: 10px;
            left: 10px;
        }

        #mode-panel {
            bottom: 10px;
            right: 10px;
        }

        #start-screen,
        #game-over-screen,
        #level-complete-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 24px;
            text-align: center;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.5s;
        }

        #start-screen.active,
        #game-over-screen.active,
        #level-complete-screen.active {
            opacity: 1;
            pointer-events: all;
        }

        .screen-title {
            font-size: 32px;
            margin-bottom: 20px;
            color: #ff0;
            text-shadow: 2px 2px 0 #f00;
        }

        .screen-text {
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .screen-button {
            background-color: #444;
            color: white;
            border: 2px solid #777;
            padding: 10px 20px;
            font-size: 18px;
            cursor: pointer;
            pointer-events: all;
            margin: 5px;
            font-family: 'Courier New', monospace;
        }

        .screen-button:hover {
            background-color: #666;
        }

        #ai-prediction {
            position: absolute;
            width: 2px;
            height: 2px;
            background-color: rgba(255, 0, 0, 0.5);
            border-radius: 50%;
            pointer-events: none;
            display: none;
        }

        #ai-trajectory {
            position: absolute;
            pointer-events: none;
            display: none;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }

        .control-button {
            background-color: #444;
            color: white;
            border: 2px solid #777;
            padding: 8px 15px;
            font-size: 14px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
        }

        .control-button:hover {
            background-color: #666;
        }

        .control-button.active {
            background-color: #0a0;
        }
    </style>
</head>

<body>
    <h1>像素弹球大师</h1>

    <div id="game-container">
        <canvas id="game-canvas" width="512" height="480"></canvas>

        <div id="ui-overlay">
            <div id="score-panel" class="ui-panel">分数: <span id="score">0</span></div>
            <div id="lives-panel" class="ui-panel">生命: <span id="lives">3</span></div>
            <div id="level-panel" class="ui-panel">关卡: <span id="level">1</span></div>
            <div id="mode-panel" class="ui-panel">模式: <span id="mode">AI演示</span></div>

            <div id="ai-prediction"></div>
            <canvas id="ai-trajectory" width="512" height="480"></canvas>

            <div id="start-screen" class="active">
                <div class="screen-title">像素弹球大师</div>
                <div class="screen-text">
                    使用方向键移动挡板<br>
                    空格键发射球<br>
                    击碎所有砖块进入下一关!
                </div>
                <button id="start-button" class="screen-button">开始游戏 (AI演示)</button>
                <button id="play-now-button" class="screen-button">立即游玩</button>
            </div>

            <div id="game-over-screen">
                <div class="screen-title">游戏结束</div>
                <div class="screen-text">
                    最终分数: <span id="final-score">0</span><br>
                    最高分数: <span id="high-score">0</span>
                </div>
                <button id="restart-button" class="screen-button">再玩一次</button>
            </div>

            <div id="level-complete-screen">
                <div class="screen-title">关卡完成!</div>
                <div class="screen-text">
                    当前分数: <span id="level-score">0</span><br>
                    下一关: <span id="next-level">2</span>
                </div>
                <button id="next-level-button" class="screen-button">继续</button>
            </div>
        </div>
    </div>

    <div class="controls">
        <button id="toggle-ai-button" class="control-button active">切换AI模式</button>
        <button id="sound-toggle-button" class="control-button active">音效: 开</button>
    </div>

    <script>
        // 游戏常量
        const GAME_WIDTH = 512;
        const GAME_HEIGHT = 480;
        const PADDLE_WIDTH = 80;
        const PADDLE_HEIGHT = 12;
        const BALL_RADIUS = 6;
        const BRICK_WIDTH = 48;
        const BRICK_HEIGHT = 20;
        const BRICK_ROWS = 5;
        const BRICK_COLS = 10;
        const BRICK_PADDING = 2;
        const BRICK_OFFSET_TOP = 60;
        const BRICK_OFFSET_LEFT = 16;
        const PADDLE_SPEED = 8;
        const INITIAL_BALL_SPEED = 4;
        const MAX_BALL_SPEED = 10;
        const AI_REACTION_TIME = 0.2; // 秒
        const AI_ERROR_MARGIN = 0.1; // 10%的误差
        const AI_MISTAKE_CHANCE = 0.05; // 5%的犯错几率

        // 游戏状态
        let gameState = {
            canvas: null,
            ctx: null,
            aiCanvas: null,
            aiCtx: null,
            gameActive: false,
            aiMode: true,
            soundEnabled: true,
            leftPressed: false,
            rightPressed: false,
            spacePressed: false,
            mouseX: 0,
            touchX: 0,
            usingTouch: false,
            score: 0,
            highScore: 0,
            lives: 3,
            level: 1,
            ball: {
                x: GAME_WIDTH / 2,
                y: GAME_HEIGHT - 50,
                dx: 0,
                dy: 0,
                speed: INITIAL_BALL_SPEED
            },
            paddle: {
                x: (GAME_WIDTH - PADDLE_WIDTH) / 2,
                y: GAME_HEIGHT - PADDLE_HEIGHT - 10,
                width: PADDLE_WIDTH,
                height: PADDLE_HEIGHT
            },
            bricks: [],
            powerUps: [],
            balls: [],
            lastTime: 0,
            aiTargetX: 0,
            aiReactionTimer: 0,
            audioContext: null,
            bgMusicNode: null,
            bgMusicPlaying: false
        };

        // 砖块类型
        const BrickType = {
            NORMAL: 1,
            STRONG: 2,
            POWERUP: 3,
            UNBREAKABLE: 4
        };

        // 道具类型
        const PowerUpType = {
            EXTRA_LIFE: 1,
            EXTRA_BALL: 2,
            WIDE_PADDLE: 3,
            SLOW_BALL: 4,
            FAST_BALL: 5
        };

        // 颜色定义 (复古8位调色板)
        const Colors = {
            BLACK: '#000000',
            WHITE: '#FFFFFF',
            RED: '#FF0000',
            GREEN: '#00FF00',
            BLUE: '#0000FF',
            YELLOW: '#FFFF00',
            CYAN: '#00FFFF',
            MAGENTA: '#FF00FF',
            ORANGE: '#FFA500',
            PINK: '#FF69B4',
            GRAY: '#808080',
            DARK_RED: '#800000',
            DARK_GREEN: '#008000',
            DARK_BLUE: '#000080',
            DARK_YELLOW: '#808000',
            DARK_CYAN: '#008080',
            DARK_MAGENTA: '#800080'
        };

        // 砖块颜色映射
        const BrickColors = {
            [BrickType.NORMAL]: Colors.RED,
            [BrickType.STRONG]: Colors.ORANGE,
            [BrickType.POWERUP]: Colors.GREEN,
            [BrickType.UNBREAKABLE]: Colors.GRAY
        };

        // 道具颜色映射
        const PowerUpColors = {
            [PowerUpType.EXTRA_LIFE]: Colors.MAGENTA,
            [PowerUpType.EXTRA_BALL]: Colors.YELLOW,
            [PowerUpType.WIDE_PADDLE]: Colors.CYAN,
            [PowerUpType.SLOW_BALL]: Colors.BLUE,
            [PowerUpType.FAST_BALL]: Colors.RED
        };

        // 初始化游戏
        function initGame() {
            gameState.canvas = document.getElementById('game-canvas');
            gameState.ctx = gameState.canvas.getContext('2d');
            gameState.aiCanvas = document.getElementById('ai-trajectory');
            gameState.aiCtx = gameState.aiCanvas.getContext('2d');

            // 初始化音频
            initAudio();

            // 设置事件监听器
            setupEventListeners();

            // 初始化游戏元素
            resetGame();

            // 开始游戏循环
            requestAnimationFrame(gameLoop);
        }

        // 初始化音频系统
        function initAudio() {
            try {
                gameState.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                console.error('Web Audio API is not supported in this browser');
                gameState.soundEnabled = false;
                document.getElementById('sound-toggle-button').classList.remove('active');
                document.getElementById('sound-toggle-button').textContent = '音效: 不支持';
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 键盘控制
            document.addEventListener('keydown', keyDownHandler);
            document.addEventListener('keyup', keyUpHandler);

            // 鼠标/触摸控制
            gameState.canvas.addEventListener('mousemove', mouseMoveHandler);
            gameState.canvas.addEventListener('touchmove', touchMoveHandler);
            gameState.canvas.addEventListener('touchstart', touchStartHandler);
            gameState.canvas.addEventListener('click', clickHandler);

            // 按钮事件
            document.getElementById('start-button').addEventListener('click', startGameAI);
            document.getElementById('play-now-button').addEventListener('click', startGamePlayer);
            document.getElementById('restart-button').addEventListener('click', resetGame);
            document.getElementById('next-level-button').addEventListener('click', nextLevel);
            document.getElementById('toggle-ai-button').addEventListener('click', toggleAIMode);
            document.getElementById('sound-toggle-button').addEventListener('click', toggleSound);
        }

        // 键盘按下处理
        function keyDownHandler(e) {
            if (e.key === 'Right' || e.key === 'ArrowRight') {
                gameState.rightPressed = true;
            } else if (e.key === 'Left' || e.key === 'ArrowLeft') {
                gameState.leftPressed = true;
            } else if (e.key === ' ') {
                gameState.spacePressed = true;
            }
        }

        // 键盘释放处理
        function keyUpHandler(e) {
            if (e.key === 'Right' || e.key === 'ArrowRight') {
                gameState.rightPressed = false;
            } else if (e.key === 'Left' || e.key === 'ArrowLeft') {
                gameState.leftPressed = false;
            } else if (e.key === ' ') {
                gameState.spacePressed = false;
            }
        }

        // 鼠标移动处理
        function mouseMoveHandler(e) {
            if (gameState.usingTouch) return;

            const relativeX = e.clientX - gameState.canvas.offsetLeft;
            if (relativeX > 0 && relativeX < gameState.canvas.width) {
                gameState.mouseX = relativeX;
            }
        }

        // 触摸移动处理
        function touchMoveHandler(e) {
            gameState.usingTouch = true;
            e.preventDefault();
            const relativeX = e.touches[0].clientX - gameState.canvas.offsetLeft;
            if (relativeX > 0 && relativeX < gameState.canvas.width) {
                gameState.touchX = relativeX;
            }
        }

        // 触摸开始处理
        function touchStartHandler(e) {
            gameState.usingTouch = true;
            const relativeX = e.touches[0].clientX - gameState.canvas.offsetLeft;
            if (relativeX > 0 && relativeX < gameState.canvas.width) {
                gameState.touchX = relativeX;
            }
        }

        // 点击处理
        function clickHandler(e) {
            if (gameState.usingTouch) return;

            if (!gameState.gameActive) {
                gameState.spacePressed = true;
            }
        }

        // 开始游戏 (AI模式)
        function startGameAI() {
            gameState.gameActive = true;
            gameState.aiMode = true;
            document.getElementById('mode').textContent = 'AI演示';
            document.getElementById('start-screen').classList.remove('active');
            resetBall();
            playSound('start');
            startBackgroundMusic();
        }

        // 开始游戏 (玩家模式)
        function startGamePlayer() {
            gameState.gameActive = true;
            gameState.aiMode = false;
            document.getElementById('mode').textContent = '玩家';
            document.getElementById('start-screen').classList.remove('active');
            resetBall();
            playSound('start');
            startBackgroundMusic();
        }

        // 切换AI模式
        function toggleAIMode() {
            gameState.aiMode = !gameState.aiMode;
            document.getElementById('mode').textContent = gameState.aiMode ? 'AI演示' : '玩家';
            document.getElementById('toggle-ai-button').classList.toggle('active');

            if (gameState.aiMode) {
                playSound('powerup');
            } else {
                playSound('powerdown');
            }
        }

        // 切换音效
        function toggleSound() {
            gameState.soundEnabled = !gameState.soundEnabled;
            document.getElementById('sound-toggle-button').classList.toggle('active');
            document.getElementById('sound-toggle-button').textContent = gameState.soundEnabled ? '音效: 开' : '音效: 关';

            if (gameState.soundEnabled) {
                playSound('powerup');
                if (gameState.gameActive && !gameState.bgMusicPlaying) {
                    startBackgroundMusic();
                }
            } else {
                stopBackgroundMusic();
            }
        }

        // 重置游戏
        function resetGame() {
            gameState.score = 0;
            gameState.lives = 3;
            gameState.level = 1;
            gameState.balls = [];

            updateUI();
            createLevel();
            resetBall();

            document.getElementById('start-screen').classList.add('active');
            document.getElementById('game-over-screen').classList.remove('active');
            document.getElementById('level-complete-screen').classList.remove('active');

            gameState.gameActive = false;
            stopBackgroundMusic();
        }

        // 下一关
        function nextLevel() {
            gameState.level++;
            document.getElementById('level').textContent = gameState.level;
            document.getElementById('next-level').textContent = gameState.level + 1;
            document.getElementById('level-complete-screen').classList.remove('active');

            createLevel();
            resetBall();

            playSound('levelup');
        }

        // 创建关卡
        function createLevel() {
            gameState.bricks = [];
            gameState.powerUps = [];

            // 根据关卡难度调整砖块布局
            const level = gameState.level;
            const rows = Math.min(BRICK_ROWS + Math.floor(level / 2), 8);
            const cols = BRICK_COLS;

            for (let c = 0; c < cols; c++) {
                for (let r = 0; r < rows; r++) {
                    // 跳过一些砖块创建随机布局
                    if (Math.random() > 0.1) {
                        let brickType = BrickType.NORMAL;
                        const rand = Math.random();

                        if (rand < 0.6) {
                            brickType = BrickType.NORMAL;
                        } else if (rand < 0.85) {
                            brickType = BrickType.STRONG;
                        } else if (rand < 0.95) {
                            brickType = BrickType.POWERUP;
                        } else {
                            brickType = BrickType.UNBREAKABLE;
                        }

                        // 第一关只生成普通砖块
                        if (level === 1) {
                            brickType = BrickType.NORMAL;
                        }

                        gameState.bricks.push({
                            x: BRICK_OFFSET_LEFT + c * (BRICK_WIDTH + BRICK_PADDING),
                            y: BRICK_OFFSET_TOP + r * (BRICK_HEIGHT + BRICK_PADDING),
                            width: BRICK_WIDTH,
                            height: BRICK_HEIGHT,
                            type: brickType,
                            hits: 0,
                            hitsRequired: brickType === BrickType.STRONG ? 2 : 1
                        });
                    }
                }
            }
        }

        // 重置球
        function resetBall() {
            gameState.ball = {
                x: GAME_WIDTH / 2,
                y: GAME_HEIGHT - 50,
                dx: 0,
                dy: 0,
                speed: INITIAL_BALL_SPEED
            };

            // 初始球方向随机
            const angle = (Math.random() * Math.PI / 2) - Math.PI / 4 + (Math.random() > 0.5 ? Math.PI : 0);
            gameState.ball.dx = Math.cos(angle) * INITIAL_BALL_SPEED;
            gameState.ball.dy = Math.sin(angle) * INITIAL_BALL_SPEED;

            // 如果只有主球且没有其他球，暂停球运动直到玩家发射
            if (gameState.balls.length === 0) {
                gameState.ball.dx = 0;
                gameState.ball.dy = 0;
            }
        }

        // 更新UI
        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('lives').textContent = gameState.lives;
            document.getElementById('level').textContent = gameState.level;
            document.getElementById('final-score').textContent = gameState.score;
            document.getElementById('high-score').textContent = Math.max(gameState.score, gameState.highScore);
            document.getElementById('level-score').textContent = gameState.score;
        }

        // 游戏结束
        function gameOver() {
            gameState.gameActive = false;
            gameState.highScore = Math.max(gameState.score, gameState.highScore);
            document.getElementById('game-over-screen').classList.add('active');
            playSound('gameover');
            stopBackgroundMusic();
        }

        // 关卡完成
        function levelComplete() {
            gameState.gameActive = false;
            document.getElementById('level-complete-screen').classList.add('active');
            playSound('win');
        }

        // 游戏主循环
        function gameLoop(timestamp) {
            // 清除画布
            gameState.ctx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
            gameState.aiCtx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

            // 计算时间增量
            const deltaTime = timestamp - gameState.lastTime;
            gameState.lastTime = timestamp;

            if (gameState.gameActive) {
                // 更新游戏状态
                updateGame(deltaTime);
            }

            // 绘制游戏
            drawGame();

            // 继续循环
            requestAnimationFrame(gameLoop);
        }

        // 更新游戏状态
        function updateGame(deltaTime) {
            // 更新挡板位置
            updatePaddle(deltaTime);

            // 更新球位置
            updateBalls(deltaTime);

            // 更新道具
            updatePowerUps(deltaTime);

            // AI控制
            if (gameState.aiMode) {
                updateAI(deltaTime);
            }

            // 检查关卡是否完成
            checkLevelCompletion();
        }

        // 更新挡板位置
        function updatePaddle(deltaTime) {
            if (gameState.aiMode) return;

            if (gameState.usingTouch) {
                // 触摸控制
                gameState.paddle.x = gameState.touchX - gameState.paddle.width / 2;
            } else if (gameState.leftPressed || gameState.rightPressed) {
                // 键盘控制
                if (gameState.leftPressed) {
                    gameState.paddle.x -= PADDLE_SPEED;
                }
                if (gameState.rightPressed) {
                    gameState.paddle.x += PADDLE_SPEED;
                }
            } else {
                // 鼠标控制
                gameState.paddle.x = gameState.mouseX - gameState.paddle.width / 2;
            }

            // 确保挡板不会移出屏幕
            if (gameState.paddle.x < 0) {
                gameState.paddle.x = 0;
            } else if (gameState.paddle.x > GAME_WIDTH - gameState.paddle.width) {
                gameState.paddle.x = GAME_WIDTH - gameState.paddle.width;
            }

            // 检查是否需要发射球
            if (gameState.spacePressed && gameState.ball.dx === 0 && gameState.ball.dy === 0) {
                const angle = (Math.random() * Math.PI / 2) - Math.PI / 4;
                gameState.ball.dx = Math.cos(angle) * INITIAL_BALL_SPEED;
                gameState.ball.dy = Math.sin(angle) * INITIAL_BALL_SPEED;
                playSound('launch');
            }
        }

        // 更新球位置
        function updateBalls(deltaTime) {
            // 更新所有球 (主球 + 额外球)
            const allBalls = [gameState.ball, ...gameState.balls];

            for (let i = 0; i < allBalls.length; i++) {
                const ball = allBalls[i];

                // 如果球没有速度，跳过更新
                if (ball.dx === 0 && ball.dy === 0) continue;

                // 更新球位置
                ball.x += ball.dx;
                ball.y += ball.dy;

                // 检测与墙壁的碰撞
                if (ball.x + ball.dx > GAME_WIDTH - BALL_RADIUS || ball.x + ball.dx < BALL_RADIUS) {
                    ball.dx = -ball.dx;
                    playSound('wall');
                }

                // 检测与天花板的碰撞
                if (ball.y + ball.dy < BALL_RADIUS) {
                    ball.dy = -ball.dy;
                    playSound('wall');
                }

                // 检测与底部的碰撞 (球丢失)
                if (ball.y + ball.dy > GAME_HEIGHT - BALL_RADIUS) {
                    // 如果是主球
                    if (i === 0) {
                        gameState.lives--;
                        updateUI();

                        if (gameState.lives <= 0) {
                            gameOver();
                        } else {
                            resetBall();
                            playSound('lose');
                        }
                    } else {
                        // 移除额外球
                        gameState.balls.splice(i - 1, 1);
                        playSound('lose');
                    }
                    continue;
                }

                // 检测与挡板的碰撞
                if (
                    ball.y + BALL_RADIUS > gameState.paddle.y &&
                    ball.y - BALL_RADIUS < gameState.paddle.y + gameState.paddle.height &&
                    ball.x + BALL_RADIUS > gameState.paddle.x &&
                    ball.x - BALL_RADIUS < gameState.paddle.x + gameState.paddle.width
                ) {
                    // 计算碰撞点相对于挡板中心的位置 (-1 到 1)
                    const hitPos = (ball.x - (gameState.paddle.x + gameState.paddle.width / 2)) / (gameState.paddle.width / 2);

                    // 根据碰撞点计算反弹角度
                    const angle = hitPos * (Math.PI / 3); // 最大60度角

                    // 计算新方向
                    const speed = Math.sqrt(ball.dx * ball.dx + ball.dy * ball.dy);
                    ball.dx = Math.sin(angle) * speed;
                    ball.dy = -Math.cos(angle) * speed;

                    // 轻微加速
                    const newSpeed = Math.min(speed * 1.02, MAX_BALL_SPEED);
                    const ratio = newSpeed / speed;
                    ball.dx *= ratio;
                    ball.dy *= ratio;

                    playSound('paddle');
                }

                // 检测与砖块的碰撞
                for (let j = 0; j < gameState.bricks.length; j++) {
                    const brick = gameState.bricks[j];

                    if (
                        ball.x + BALL_RADIUS > brick.x &&
                        ball.x - BALL_RADIUS < brick.x + brick.width &&
                        ball.y + BALL_RADIUS > brick.y &&
                        ball.y - BALL_RADIUS < brick.y + brick.height
                    ) {
                        // 确定碰撞面
                        const ballCenterX = ball.x;
                        const ballCenterY = ball.y;
                        const brickCenterX = brick.x + brick.width / 2;
                        const brickCenterY = brick.y + brick.height / 2;

                        // 计算重叠量
                        const overlapX = (BALL_RADIUS + brick.width / 2) - Math.abs(ballCenterX - brickCenterX);
                        const overlapY = (BALL_RADIUS + brick.height / 2) - Math.abs(ballCenterY - brickCenterY);

                        // 从最小重叠方向反弹
                        if (overlapX < overlapY) {
                            ball.dx = -ball.dx;
                        } else {
                            ball.dy = -ball.dy;
                        }

                        // 处理砖块被击中
                        if (brick.type !== BrickType.UNBREAKABLE) {
                            brick.hits++;

                            if (brick.hits >= brick.hitsRequired) {
                                // 砖块被摧毁
                                const scoreValue = brick.type === BrickType.NORMAL ? 10 :
                                    brick.type === BrickType.STRONG ? 20 :
                                        brick.type === BrickType.POWERUP ? 30 : 0;

                                gameState.score += scoreValue;
                                updateUI();

                                // 检查是否掉落道具
                                if (brick.type === BrickType.POWERUP && Math.random() < 0.5) {
                                    createPowerUp(brick.x + brick.width / 2, brick.y + brick.height / 2);
                                }

                                gameState.bricks.splice(j, 1);
                                j--;

                                playSound('brickBreak');
                            } else {
                                playSound('brickHit');
                            }
                        } else {
                            playSound('brickHit');
                        }

                        break; // 一次只处理一个砖块碰撞
                    }
                }
            }
        }

        // 创建道具
        function createPowerUp(x, y) {
            const types = [
                PowerUpType.EXTRA_LIFE,
                PowerUpType.EXTRA_BALL,
                PowerUpType.WIDE_PADDLE,
                PowerUpType.SLOW_BALL,
                PowerUpType.FAST_BALL
            ];

            const type = types[Math.floor(Math.random() * types.length)];

            gameState.powerUps.push({
                x: x,
                y: y,
                width: 20,
                height: 10,
                type: type,
                speed: 2
            });
        }

        // 更新道具
        function updatePowerUps(deltaTime) {
            for (let i = 0; i < gameState.powerUps.length; i++) {
                const powerUp = gameState.powerUps[i];

                // 移动道具
                powerUp.y += powerUp.speed;

                // 检测与挡板的碰撞
                if (
                    powerUp.y + powerUp.height > gameState.paddle.y &&
                    powerUp.y < gameState.paddle.y + gameState.paddle.height &&
                    powerUp.x + powerUp.width > gameState.paddle.x &&
                    powerUp.x < gameState.paddle.x + gameState.paddle.width
                ) {
                    // 应用道具效果
                    applyPowerUp(powerUp.type);
                    gameState.powerUps.splice(i, 1);
                    i--;
                    continue;
                }

                // 检测道具是否掉出屏幕
                if (powerUp.y > GAME_HEIGHT) {
                    gameState.powerUps.splice(i, 1);
                    i--;
                }
            }
        }

        // 应用道具效果
        function applyPowerUp(type) {
            playSound('powerup');

            switch (type) {
                case PowerUpType.EXTRA_LIFE:
                    gameState.lives++;
                    updateUI();
                    break;

                case PowerUpType.EXTRA_BALL:
                    const newBall = {
                        x: gameState.ball.x,
                        y: gameState.ball.y,
                        dx: -gameState.ball.dx,
                        dy: gameState.ball.dy,
                        speed: gameState.ball.speed
                    };
                    gameState.balls.push(newBall);
                    break;

                case PowerUpType.WIDE_PADDLE:
                    gameState.paddle.width = 120;
                    setTimeout(() => {
                        gameState.paddle.width = PADDLE_WIDTH;
                    }, 10000);
                    break;

                case PowerUpType.SLOW_BALL:
                    const allBalls = [gameState.ball, ...gameState.balls];
                    for (const ball of allBalls) {
                        const speed = Math.sqrt(ball.dx * ball.dx + ball.dy * ball.dy);
                        if (speed > 3) {
                            const ratio = 3 / speed;
                            ball.dx *= ratio;
                            ball.dy *= ratio;
                        }
                    }
                    setTimeout(() => {
                        const allBalls = [gameState.ball, ...gameState.balls];
                        for (const ball of allBalls) {
                            const speed = Math.sqrt(ball.dx * ball.dx + ball.dy * ball.dy);
                            if (speed < INITIAL_BALL_SPEED) {
                                const ratio = INITIAL_BALL_SPEED / speed;
                                ball.dx *= ratio;
                                ball.dy *= ratio;
                            }
                        }
                    }, 5000);
                    break;

                case PowerUpType.FAST_BALL:
                    const allBallsFast = [gameState.ball, ...gameState.balls];
                    for (const ball of allBallsFast) {
                        const speed = Math.sqrt(ball.dx * ball.dx + ball.dy * ball.dy);
                        if (speed < MAX_BALL_SPEED) {
                            const ratio = Math.min(MAX_BALL_SPEED, speed * 1.5) / speed;
                            ball.dx *= ratio;
                            ball.dy *= ratio;
                        }
                    }
                    setTimeout(() => {
                        const allBallsFast = [gameState.ball, ...gameState.balls];
                        for (const ball of allBallsFast) {
                            const speed = Math.sqrt(ball.dx * ball.dx + ball.dy * ball.dy);
                            if (speed > INITIAL_BALL_SPEED) {
                                const ratio = INITIAL_BALL_SPEED / speed;
                                ball.dx *= ratio;
                                ball.dy *= ratio;
                            }
                        }
                    }, 5000);
                    break;
            }
        }

        // 检查关卡是否完成
        function checkLevelCompletion() {
            // 检查是否还有可破坏的砖块
            let breakableBricksLeft = false;
            for (const brick of gameState.bricks) {
                if (brick.type !== BrickType.UNBREAKABLE) {
                    breakableBricksLeft = true;
                    break;
                }
            }

            if (!breakableBricksLeft) {
                levelComplete();
            }
        }

        // 更新AI控制
        function updateAI(deltaTime) {
            // 将时间增量转换为秒
            const deltaTimeSeconds = deltaTime / 1000;

            // 更新AI反应计时器
            gameState.aiReactionTimer += deltaTimeSeconds;

            // 只在反应时间过后才更新目标位置
            if (gameState.aiReactionTimer >= AI_REACTION_TIME) {
                gameState.aiReactionTimer = 0;

                // 预测球的落点
                const prediction = predictBallLanding(gameState.ball);
                gameState.aiTargetX = prediction.x;

                // 添加一些随机错误
                if (Math.random() < AI_MISTAKE_CHANCE) {
                    gameState.aiTargetX += (Math.random() - 0.5) * gameState.paddle.width * 2;
                }

                // 确保目标位置在屏幕内
                gameState.aiTargetX = Math.max(gameState.paddle.width / 2,
                    Math.min(GAME_WIDTH - gameState.paddle.width / 2, gameState.aiTargetX));

                // 显示预测轨迹 (调试用)
                if (gameState.soundEnabled) {
                    drawAIPrediction(prediction);
                }
            }

            // 移动挡板朝向目标位置
            const paddleCenter = gameState.paddle.x + gameState.paddle.width / 2;
            const direction = gameState.aiTargetX - paddleCenter;

            // 添加误差范围
            const errorMargin = gameState.paddle.width * AI_ERROR_MARGIN;
            if (Math.abs(direction) > errorMargin) {
                gameState.paddle.x += Math.sign(direction) * PADDLE_SPEED;
            }

            // 确保挡板不会移出屏幕
            if (gameState.paddle.x < 0) {
                gameState.paddle.x = 0;
            } else if (gameState.paddle.x > GAME_WIDTH - gameState.paddle.width) {
                gameState.paddle.x = GAME_WIDTH - gameState.paddle.width;
            }

            // 自动发射球
            if (gameState.ball.dx === 0 && gameState.ball.dy === 0) {
                const angle = (Math.random() * Math.PI / 2) - Math.PI / 4;
                gameState.ball.dx = Math.cos(angle) * INITIAL_BALL_SPEED;
                gameState.ball.dy = Math.sin(angle) * INITIAL_BALL_SPEED;
                playSound('launch');
            }
        }

        // 预测球落点
        function predictBallLanding(ball) {
            // 如果球向上运动，我们需要模拟反弹直到它向下运动
            let x = ball.x;
            let y = ball.y;
            let dx = ball.dx;
            let dy = ball.dy;
            let speed = Math.sqrt(dx * dx + dy * dy);

            // 模拟最多100步，防止无限循环
            for (let i = 0; i < 100; i++) {
                // 移动球
                x += dx;
                y += dy;

                // 检查与墙壁的碰撞
                if (x + dx > GAME_WIDTH - BALL_RADIUS || x + dx < BALL_RADIUS) {
                    dx = -dx;
                }

                // 检查与天花板的碰撞
                if (y + dy < BALL_RADIUS) {
                    dy = -dy;
                }

                // 检查是否到达挡板高度
                if (y + dy > gameState.paddle.y - BALL_RADIUS) {
                    // 计算预测的x位置
                    const remainingDistance = (gameState.paddle.y - BALL_RADIUS) - y;
                    const steps = remainingDistance / dy;
                    const predictedX = x + dx * steps;

                    // 返回预测位置
                    return {
                        x: predictedX,
                        y: gameState.paddle.y - BALL_RADIUS
                    };
                }

                // 检查与砖块的碰撞 (简化版，只考虑垂直反弹)
                for (const brick of gameState.bricks) {
                    if (
                        x + BALL_RADIUS > brick.x &&
                        x - BALL_RADIUS < brick.x + brick.width &&
                        y + BALL_RADIUS > brick.y &&
                        y - BALL_RADIUS < brick.y + brick.height
                    ) {
                        // 简单假设垂直反弹
                        dy = -dy;
                        break;
                    }
                }
            }

            // 如果无法预测，返回当前x位置
            return {
                x: ball.x,
                y: GAME_HEIGHT - BALL_RADIUS
            };
        }

        // 绘制AI预测
        function drawAIPrediction(prediction) {
            // 清除之前的轨迹
            gameState.aiCtx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

            // 绘制预测落点
            gameState.aiCtx.beginPath();
            gameState.aiCtx.arc(prediction.x, prediction.y, 5, 0, Math.PI * 2);
            gameState.aiCtx.fillStyle = 'rgba(255, 0, 0, 0.5)';
            gameState.aiCtx.fill();

            // 绘制从球到预测点的轨迹线
            gameState.aiCtx.beginPath();
            gameState.aiCtx.moveTo(gameState.ball.x, gameState.ball.y);
            gameState.aiCtx.lineTo(prediction.x, prediction.y);
            gameState.aiCtx.strokeStyle = 'rgba(255, 0, 0, 0.3)';
            gameState.aiCtx.stroke();

            // 短暂显示后淡出
            setTimeout(() => {
                gameState.aiCtx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
            }, 200);
        }

        // 绘制游戏
        function drawGame() {
            // 绘制背景
            gameState.ctx.fillStyle = Colors.BLACK;
            gameState.ctx.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

            // 绘制装饰边框
            drawBorder();

            // 绘制砖块
            drawBricks();

            // 绘制道具
            drawPowerUps();

            // 绘制挡板
            drawPaddle();

            // 绘制球
            drawBalls();
        }

        // 绘制装饰边框
        function drawBorder() {
            gameState.ctx.strokeStyle = Colors.DARK_BLUE;
            gameState.ctx.lineWidth = 4;
            gameState.ctx.strokeRect(2, 2, GAME_WIDTH - 4, GAME_HEIGHT - 4);

            // 绘制复古像素装饰
            gameState.ctx.fillStyle = Colors.DARK_RED;
            for (let i = 0; i < 10; i++) {
                gameState.ctx.fillRect(10 + i * 50, 10, 4, 4);
                gameState.ctx.fillRect(10 + i * 50, GAME_HEIGHT - 14, 4, 4);
            }
        }

        // 绘制砖块
        function drawBricks() {
            for (const brick of gameState.bricks) {
                // 根据砖块类型设置颜色
                gameState.ctx.fillStyle = BrickColors[brick.type];

                // 绘制砖块
                gameState.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);

                // 绘制砖块边框
                gameState.ctx.strokeStyle = Colors.BLACK;
                gameState.ctx.lineWidth = 1;
                gameState.ctx.strokeRect(brick.x, brick.y, brick.width, brick.height);

                // 对于坚固砖块，显示击中次数
                if (brick.type === BrickType.STRONG && brick.hits < brick.hitsRequired) {
                    gameState.ctx.fillStyle = Colors.WHITE;
                    gameState.ctx.font = '10px Arial';
                    gameState.ctx.textAlign = 'center';
                    gameState.ctx.fillText(
                        `${brick.hitsRequired - brick.hits}`,
                        brick.x + brick.width / 2,
                        brick.y + brick.height / 2 + 3
                    );
                }
            }
        }

        // 绘制道具
        function drawPowerUps() {
            for (const powerUp of gameState.powerUps) {
                // 根据道具类型设置颜色
                gameState.ctx.fillStyle = PowerUpColors[powerUp.type];

                // 绘制道具
                gameState.ctx.fillRect(powerUp.x, powerUp.y, powerUp.width, powerUp.height);

                // 绘制道具边框
                gameState.ctx.strokeStyle = Colors.WHITE;
                gameState.ctx.lineWidth = 1;
                gameState.ctx.strokeRect(powerUp.x, powerUp.y, powerUp.width, powerUp.height);

                // 绘制道具图标 (简单表示)
                gameState.ctx.fillStyle = Colors.WHITE;
                gameState.ctx.font = '8px Arial';
                gameState.ctx.textAlign = 'center';

                let symbol = '';
                switch (powerUp.type) {
                    case PowerUpType.EXTRA_LIFE: symbol = 'L'; break;
                    case PowerUpType.EXTRA_BALL: symbol = 'B'; break;
                    case PowerUpType.WIDE_PADDLE: symbol = 'W'; break;
                    case PowerUpType.SLOW_BALL: symbol = 'S'; break;
                    case PowerUpType.FAST_BALL: symbol = 'F'; break;
                }

                gameState.ctx.fillText(
                    symbol,
                    powerUp.x + powerUp.width / 2,
                    powerUp.y + powerUp.height / 2 + 3
                );
            }
        }

        // 绘制挡板
        function drawPaddle() {
            gameState.ctx.fillStyle = Colors.WHITE;
            gameState.ctx.fillRect(
                gameState.paddle.x,
                gameState.paddle.y,
                gameState.paddle.width,
                gameState.paddle.height
            );

            // 绘制挡板细节
            gameState.ctx.fillStyle = Colors.CYAN;
            gameState.ctx.fillRect(
                gameState.paddle.x + 2,
                gameState.paddle.y + 2,
                gameState.paddle.width - 4,
                gameState.paddle.height - 4
            );
        }

        // 绘制球
        function drawBalls() {
            // 绘制所有球 (主球 + 额外球)
            const allBalls = [gameState.ball, ...gameState.balls];

            for (const ball of allBalls) {
                // 绘制球
                gameState.ctx.beginPath();
                gameState.ctx.arc(ball.x, ball.y, BALL_RADIUS, 0, Math.PI * 2);
                gameState.ctx.fillStyle = Colors.WHITE;
                gameState.ctx.fill();

                // 绘制球的高光效果
                gameState.ctx.beginPath();
                gameState.ctx.arc(
                    ball.x - BALL_RADIUS / 3,
                    ball.y - BALL_RADIUS / 3,
                    BALL_RADIUS / 4,
                    0,
                    Math.PI * 2
                );
                gameState.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                gameState.ctx.fill();
            }
        }

        // 播放音效
        function playSound(type) {
            if (!gameState.soundEnabled || !gameState.audioContext) return;

            const now = gameState.audioContext.currentTime;
            const oscillator = gameState.audioContext.createOscillator();
            const gainNode = gameState.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(gameState.audioContext.destination);

            // 设置音色
            switch (type) {
                case 'wall':
                case 'paddle':
                    oscillator.type = 'square';
                    break;
                case 'brickHit':
                    oscillator.type = 'triangle';
                    break;
                case 'brickBreak':
                    oscillator.type = 'sawtooth';
                    break;
                case 'powerup':
                    oscillator.type = 'sine';
                    break;
                case 'lose':
                case 'gameover':
                    oscillator.type = 'sine';
                    break;
                case 'win':
                case 'levelup':
                    oscillator.type = 'square';
                    break;
                case 'launch':
                    oscillator.type = 'square';
                    break;
                default:
                    oscillator.type = 'square';
            }

            // 设置频率和音量包络
            let frequency = 440;
            let attack = 0.01;
            let decay = 0.1;
            let sustain = 0.5;
            let release = 0.1;
            let duration = 0.2;

            switch (type) {
                case 'wall':
                    frequency = 220 + Math.random() * 110;
                    duration = 0.1;
                    break;
                case 'paddle':
                    frequency = 330 + Math.random() * 110;
                    duration = 0.1;
                    break;
                case 'brickHit':
                    frequency = 110 + Math.random() * 55;
                    duration = 0.15;
                    sustain = 0.3;
                    break;
                case 'brickBreak':
                    frequency = 880;
                    attack = 0.005;
                    decay = 0.2;
                    sustain = 0;
                    release = 0.1;
                    duration = 0.3;
                    break;
                case 'powerup':
                    frequency = 1760;
                    attack = 0.01;
                    decay = 0.2;
                    sustain = 0.3;
                    release = 0.2;
                    duration = 0.5;
                    break;
                case 'lose':
                    frequency = 110;
                    attack = 0.05;
                    decay = 0.3;
                    sustain = 0;
                    release = 0.2;
                    duration = 0.5;
                    break;
                case 'gameover':
                    // 下降音调
                    oscillator.frequency.setValueAtTime(440, now);
                    oscillator.frequency.exponentialRampToValueAtTime(110, now + 1);
                    attack = 0.1;
                    decay = 0.9;
                    sustain = 0;
                    release = 0;
                    duration = 1;
                    break;
                case 'win':
                    // 上升音调序列
                    const freqs = [440, 554.37, 659.26, 880];
                    let currentTime = now;
                    freqs.forEach((freq, i) => {
                        oscillator.frequency.setValueAtTime(freq, currentTime);
                        currentTime += 0.1;
                    });
                    attack = 0.01;
                    decay = 0.1;
                    sustain = 0.7;
                    release = 0.2;
                    duration = 0.5;
                    break;
                case 'levelup':
                    // 欢快的音调序列
                    const freqsLevel = [523.25, 659.26, 783.99, 1046.5];
                    let currentTimeLevel = now;
                    freqsLevel.forEach((freq, i) => {
                        oscillator.frequency.setValueAtTime(freq, currentTimeLevel);
                        currentTimeLevel += 0.1;
                    });
                    attack = 0.01;
                    decay = 0.1;
                    sustain = 0.7;
                    release = 0.2;
                    duration = 0.5;
                    break;
                case 'launch':
                    frequency = 220;
                    attack = 0.01;
                    decay = 0.2;
                    sustain = 0;
                    release = 0.1;
                    duration = 0.3;
                    break;
                case 'start':
                    // 开始游戏音效 - 上升音阶
                    const startFreqs = [261.63, 329.63, 392.00, 523.25];
                    let startTime = now;
                    startFreqs.forEach((freq, i) => {
                        oscillator.frequency.setValueAtTime(freq, startTime);
                        startTime += 0.1;
                    });
                    attack = 0.01;
                    decay = 0.1;
                    sustain = 0.7;
                    release = 0.2;
                    duration = 0.5;
                    break;
                case 'powerdown':
                    frequency = 880;
                    oscillator.frequency.exponentialRampToValueAtTime(220, now + 0.3);
                    attack = 0.01;
                    decay = 0.3;
                    sustain = 0;
                    release = 0;
                    duration = 0.3;
                    break;
            }

            // 设置音量包络
            gainNode.gain.setValueAtTime(0, now);
            gainNode.gain.linearRampToValueAtTime(0.5, now + attack);
            gainNode.gain.linearRampToValueAtTime(sustain, now + attack + decay);
            gainNode.gain.setValueAtTime(sustain, now + duration - release);
            gainNode.gain.linearRampToValueAtTime(0, now + duration);

            // 设置频率
            if (type !== 'gameover' && type !== 'win' && type !== 'levelup' && type !== 'start' && type !== 'powerdown') {
                oscillator.frequency.setValueAtTime(frequency, now);
            }

            // 启动和停止振荡器
            oscillator.start(now);
            oscillator.stop(now + duration);
        }

        // 开始背景音乐
        function startBackgroundMusic() {
            if (!gameState.soundEnabled || !gameState.audioContext || gameState.bgMusicPlaying) return;

            gameState.bgMusicPlaying = true;

            // 创建音频节点
            const oscillator1 = gameState.audioContext.createOscillator();
            const oscillator2 = gameState.audioContext.createOscillator();
            const gainNode1 = gameState.audioContext.createGain();
            const gainNode2 = gameState.audioContext.createGain();
            const merger = gameState.audioContext.createChannelMerger(2);

            oscillator1.connect(gainNode1);
            oscillator2.connect(gainNode2);
            gainNode1.connect(merger, 0, 0);
            gainNode2.connect(merger, 0, 1);
            merger.connect(gameState.audioContext.destination);

            // 设置振荡器
            oscillator1.type = 'square';
            oscillator2.type = 'triangle';

            // 简单的8位风格音乐序列
            const melody = [
                { note: 'C4', duration: 0.5 },
                { note: 'E4', duration: 0.5 },
                { note: 'G4', duration: 0.5 },
                { note: 'C5', duration: 0.5 },
                { note: 'E5', duration: 0.5 },
                { note: 'G4', duration: 0.5 },
                { note: 'E5', duration: 0.5 },
                { note: 'C5', duration: 0.5 }
            ];

            const noteFrequencies = {
                'C4': 261.63,
                'E4': 329.63,
                'G4': 392.00,
                'C5': 523.25,
                'E5': 659.25
            };

            let currentTime = gameState.audioContext.currentTime;

            // 播放旋律循环
            function playLoop() {
                currentTime += 0.1; // 短暂延迟

                for (let i = 0; i < melody.length; i++) {
                    const note = melody[i];
                    const frequency = noteFrequencies[note.note];

                    // 主音 (方波)
                    oscillator1.frequency.setValueAtTime(frequency, currentTime);
                    gainNode1.gain.setValueAtTime(0.1, currentTime);
                    gainNode1.gain.linearRampToValueAtTime(0.1, currentTime + note.duration * 0.9);
                    gainNode1.gain.linearRampToValueAtTime(0, currentTime + note.duration);

                    // 和声 (三角波，低八度)
                    oscillator2.frequency.setValueAtTime(frequency / 2, currentTime);
                    gainNode2.gain.setValueAtTime(0.05, currentTime);
                    gainNode2.gain.linearRampToValueAtTime(0.05, currentTime + note.duration * 0.9);
                    gainNode2.gain.linearRampToValueAtTime(0, currentTime + note.duration);

                    currentTime += note.duration;
                }

                // 设置循环
                if (gameState.bgMusicPlaying) {
                    setTimeout(playLoop, (currentTime - gameState.audioContext.currentTime) * 1000);
                }
            }

            // 启动振荡器
            oscillator1.start();
            oscillator2.start();

            // 开始播放循环
            playLoop();

            // 保存节点引用以便停止
            gameState.bgMusicNode = { oscillator1, oscillator2, gainNode1, gainNode2 };
        }

        // 停止背景音乐
        function stopBackgroundMusic() {
            if (!gameState.bgMusicPlaying || !gameState.bgMusicNode) return;

            gameState.bgMusicPlaying = false;

            // 淡出音乐
            const now = gameState.audioContext.currentTime;
            gameState.bgMusicNode.gainNode1.gain.linearRampToValueAtTime(0, now + 0.5);
            gameState.bgMusicNode.gainNode2.gain.linearRampToValueAtTime(0, now + 0.5);

            // 停止振荡器
            setTimeout(() => {
                gameState.bgMusicNode.oscillator1.stop();
                gameState.bgMusicNode.oscillator2.stop();
                gameState.bgMusicNode = null;
            }, 500);
        }

        // 启动游戏
        window.onload = initGame;
    </script>
</body>

</html>