<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>像素弹球大师 - 红白机风格</title>
    <style>
        @font-face {
            font-family: 'PixelFont';
            src: url('data:font/woff2;base64,') format('woff2');
        }

        body {
            margin: 0;
            padding: 0;
            background: #1a1a2e;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Courier New', monospace;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        #gameContainer {
            position: relative;
            background: #000;
            border: 4px solid #fff;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        #gameCanvas {
            display: block;
            background: #000;
            image-rendering: pixelated;
        }

        #ui {
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            color: #fff;
            font-size: 16px;
            font-weight: bold;
        }

        #controls {
            position: absolute;
            bottom: -50px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        button {
            background: #333;
            color: #fff;
            border: 2px solid #fff;
            padding: 8px 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        button:hover {
            background: #555;
            transform: scale(1.05);
        }

        button:active {
            transform: scale(0.95);
        }

        #gameOverScreen,
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 20px;
        }

        .hidden {
            display: none !important;
        }

        #aiIndicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #0f0;
            font-size: 12px;
            opacity: 0.7;
        }
    </style>
</head>

<body>
    <div id="gameContainer">
        <div id="ui">
            <span>分数: <span id="score">0</span></span>
            <span>最高分: <span id="highScore">0</span></span>
            <span>生命: <span id="lives">3</span></span>
            <span>关卡: <span id="level">1</span></span>
        </div>
        <canvas id="gameCanvas" width="256" height="240"></canvas>
        <div id="aiIndicator" class="hidden">AI 演示中</div>

        <div id="startScreen">
            <h1 style="color: #0f0; margin-bottom: 20px;">像素弹球大师</h1>
            <p>← → 移动挡板 | 空格键发球</p>
            <button onclick="startGame()">开始游戏</button>
            <button onclick="toggleAI()">切换 AI 模式</button>
        </div>

        <div id="gameOverScreen" class="hidden">
            <h2>游戏结束</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">重新开始</button>
        </div>

        <div id="controls">
            <button onclick="toggleAI()">AI 演示: <span id="aiStatus">开启</span></button>
            <button onclick="toggleSound()">音效: <span id="soundStatus">开启</span></button>
        </div>
    </div>

    <script>
        // 游戏配置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        ctx.imageSmoothingEnabled = false;

        // 游戏状态
        let gameState = 'start'; // start, playing, paused, gameOver
        let score = 0;
        let highScore = localStorage.getItem('pixelPongHighScore') || 0;
        let lives = 3;
        let level = 1;
        let aiMode = true;
        let soundEnabled = true;

        // 颜色调色板
        const colors = {
            bg: '#000',
            paddle: '#0f0',
            ball: '#fff',
            brick1: '#f00',
            brick2: '#ff0',
            brick3: '#0ff',
            brick4: '#f0f',
            powerup: '#0f0',
            text: '#fff',
            trail: '#555'
        };

        // 音频上下文
        let audioCtx;
        let masterGain;

        // 初始化音频
        function initAudio() {
            audioCtx = new (window.AudioContext || window.webkitAudioContext)();
            masterGain = audioCtx.createGain();
            masterGain.connect(audioCtx.destination);
            masterGain.gain.value = 0.3;
        }

        // 音效生成函数
        function playSound(frequency, duration, type = 'square', volume = 0.1) {
            if (!soundEnabled || !audioCtx) return;

            const oscillator = audioCtx.createOscillator();
            const gainNode = audioCtx.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(masterGain);

            oscillator.type = type;
            oscillator.frequency.setValueAtTime(frequency, audioCtx.currentTime);

            gainNode.gain.setValueAtTime(0, audioCtx.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, audioCtx.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioCtx.currentTime + duration);

            oscillator.start(audioCtx.currentTime);
            oscillator.stop(audioCtx.currentTime + duration);
        }

        // 游戏对象
        const paddle = {
            x: 108,
            y: 220,
            width: 40,
            height: 8,
            speed: 3,
            targetX: 108
        };

        const ball = {
            x: 128,
            y: 200,
            vx: 2,
            vy: -2,
            radius: 3,
            trail: []
        };

        let bricks = [];
        let powerups = [];
        let particles = [];

        // 砖块类型
        const brickTypes = {
            normal: { color: colors.brick1, hits: 1, points: 10 },
            strong: { color: colors.brick2, hits: 2, points: 30 },
            stronger: { color: colors.brick3, hits: 3, points: 50 },
            power: { color: colors.brick4, hits: 1, points: 20, hasPowerup: true }
        };

        // 道具类型
        const powerupTypes = {
            multi: { color: '#0f0', effect: 'multiball' },
            wide: { color: '#ff0', effect: 'widen' },
            slow: { color: '#0ff', effect: 'slow' },
            penetrate: { color: '#f0f', effect: 'penetrate' }
        };

        // 关卡设计
        const levels = [
            // 关卡1
            [
                '1111111111',
                '2222222222',
                '3333333333',
                '4444444444'
            ],
            // 关卡2
            [
                '1111111111',
                '0222222220',
                '0033333300',
                '0004440000'
            ],
            // 关卡3
            [
                '1234123412',
                '2341234123',
                '3412341234',
                '4123412341'
            ]
        ];

        // 创建砖块
        function createBricks(levelIndex) {
            bricks = [];
            const levelData = levels[levelIndex - 1];
            const brickWidth = 22;
            const brickHeight = 8;
            const gap = 2;

            for (let row = 0; row < levelData.length; row++) {
                for (let col = 0; col < levelData[row].length; col++) {
                    const type = parseInt(levelData[row][col]);
                    if (type > 0) {
                        const brickTypesArray = [null, 'normal', 'strong', 'stronger', 'power'];
                        const brickType = brickTypesArray[type];
                        if (brickType) {
                            bricks.push({
                                x: col * (brickWidth + gap) + 10,
                                y: row * (brickHeight + gap) + 30,
                                width: brickWidth,
                                height: brickHeight,
                                type: brickType,
                                hits: brickTypes[brickType].hits,
                                maxHits: brickTypes[brickType].hits
                            });
                        }
                    }
                }
            }
        }

        // AI系统
        const ai = {
            targetX: 128,
            reactionDelay: 0.2,
            errorMargin: 10,
            lastUpdate: 0,
            predictBall() {
                let futureX = ball.x;
                let futureY = ball.y;
                let futureVx = ball.vx;
                let futureVy = ball.vy;

                while (futureY < paddle.y - 10) {
                    futureX += futureVx;
                    futureY += futureVy;

                    if (futureX <= 3 || futureX >= 253) {
                        futureVx = -futureVx;
                    }
                    if (futureY <= 3) {
                        futureVy = -futureVy;
                    }
                }

                return futureX;
            },
            update() {
                if (!aiMode) return;

                const now = Date.now();
                if (now - this.lastUpdate > this.reactionDelay * 1000) {
                    const predictedX = this.predictBall();
                    const error = (Math.random() - 0.5) * this.errorMargin;
                    this.targetX = predictedX + error;
                    this.targetX = Math.max(0, Math.min(216, this.targetX));
                    this.lastUpdate = now;
                }

                const diff = this.targetX - paddle.x;
                paddle.x += Math.sign(diff) * Math.min(Math.abs(diff), paddle.speed);
            }
        };

        // 粒子效果
        function createParticles(x, y, color, count = 5) {
            for (let i = 0; i < count; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    color: color,
                    life: 30
                });
            }
        }

        // 碰撞检测
        function checkBallPaddle() {
            if (ball.y + ball.radius >= paddle.y &&
                ball.y - ball.radius <= paddle.y + paddle.height &&
                ball.x >= paddle.x &&
                ball.x <= paddle.x + paddle.width) {

                // 根据碰撞位置调整角度
                const hitPos = (ball.x - paddle.x) / paddle.width;
                const angle = (hitPos - 0.5) * Math.PI / 3;
                const speed = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy);

                ball.vx = speed * Math.sin(angle);
                ball.vy = -Math.abs(speed * Math.cos(angle));

                playSound(220, 0.1, 'square');
                createParticles(ball.x, ball.y, colors.paddle);
            }
        }

        function checkBallBricks() {
            for (let i = bricks.length - 1; i >= 0; i--) {
                const brick = bricks[i];

                if (ball.x + ball.radius >= brick.x &&
                    ball.x - ball.radius <= brick.x + brick.width &&
                    ball.y + ball.radius >= brick.y &&
                    ball.y - ball.radius <= brick.y + brick.height) {

                    // 确定碰撞面
                    const ballCenterX = ball.x;
                    const ballCenterY = ball.y;
                    const brickCenterX = brick.x + brick.width / 2;
                    const brickCenterY = brick.y + brick.height / 2;

                    const dx = Math.abs(ballCenterX - brickCenterX) / (brick.width / 2);
                    const dy = Math.abs(ballCenterY - brickCenterY) / (brick.height / 2);

                    if (dx > dy) {
                        ball.vx = -ball.vx;
                    } else {
                        ball.vy = -ball.vy;
                    }

                    brick.hits--;

                    if (brick.hits <= 0) {
                        score += brickTypes[brick.type].points;

                        // 创建道具
                        if (brickTypes[brick.type].hasPowerup && Math.random() < 0.3) {
                            const powerupKeys = Object.keys(powerupTypes);
                            const randomType = powerupKeys[Math.floor(Math.random() * powerupKeys.length)];
                            powerups.push({
                                x: brick.x + brick.width / 2,
                                y: brick.y + brick.height / 2,
                                vy: 1,
                                type: randomType,
                                ...powerupTypes[randomType]
                            });
                        }

                        createParticles(brick.x + brick.width / 2, brick.y + brick.height / 2,
                            brickTypes[brick.type].color);
                        bricks.splice(i, 1);
                        playSound(440 + Math.random() * 200, 0.15, 'square');
                    } else {
                        playSound(330, 0.1, 'square');
                    }

                    break;
                }
            }
        }

        function checkPowerups() {
            for (let i = powerups.length - 1; i >= 0; i--) {
                const powerup = powerups[i];
                powerup.y += powerup.vy;

                if (powerup.y >= paddle.y &&
                    powerup.y <= paddle.y + paddle.height &&
                    powerup.x >= paddle.x &&
                    powerup.x <= paddle.x + paddle.width) {

                    applyPowerup(powerup.type);
                    powerups.splice(i, 1);
                    playSound(880, 0.2, 'triangle');
                } else if (powerup.y > 240) {
                    powerups.splice(i, 1);
                }
            }
        }

        function applyPowerup(type) {
            switch (type) {
                case 'multiball':
                    // 实现多球
                    break;
                case 'widen':
                    paddle.width = Math.min(60, paddle.width + 10);
                    break;
                case 'slow':
                    ball.vx *= 0.7;
                    ball.vy *= 0.7;
                    break;
                case 'penetrate':
                    // 实现穿透
                    break;
            }
        }

        // 游戏循环
        function update() {
            if (gameState !== 'playing') return;

            // 更新球位置
            ball.x += ball.vx;
            ball.y += ball.vy;

            // 添加轨迹
            ball.trail.push({ x: ball.x, y: ball.y });
            if (ball.trail.length > 5) ball.trail.shift();

            // 边界碰撞
            if (ball.x <= 3 || ball.x >= 253) {
                ball.vx = -ball.vx;
                playSound(330, 0.1, 'square');
            }
            if (ball.y <= 3) {
                ball.vy = -ball.vy;
                playSound(330, 0.1, 'square');
            }

            // 底部边界
            if (ball.y > 240) {
                lives--;
                if (lives <= 0) {
                    gameOver();
                } else {
                    resetBall();
                    playSound(110, 0.5, 'sawtooth');
                }
            }

            // 碰撞检测
            checkBallPaddle();
            checkBallBricks();
            checkPowerups();

            // 更新AI
            ai.update();

            // 更新粒子
            particles.forEach(p => {
                p.x += p.vx;
                p.y += p.vy;
                p.life--;
            });
            particles = particles.filter(p => p.life > 0);

            // 检查关卡完成
            if (bricks.length === 0) {
                nextLevel();
            }

            // 更新UI
            updateUI();
        }

        function render() {
            // 清空画布
            ctx.fillStyle = colors.bg;
            ctx.fillRect(0, 0, 256, 240);

            // 绘制网格背景
            ctx.strokeStyle = '#111';
            ctx.lineWidth = 1;
            for (let i = 0; i < 256; i += 16) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, 240);
                ctx.stroke();
            }
            for (let i = 0; i < 240; i += 16) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(256, i);
                ctx.stroke();
            }

            // 绘制砖块
            bricks.forEach(brick => {
                const alpha = brick.hits / brick.maxHits;
                ctx.fillStyle = brickTypes[brick.type].color;
                ctx.globalAlpha = alpha;
                ctx.fillRect(brick.x, brick.y, brick.width, brick.height);

                // 砖块边框
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 1;
                ctx.globalAlpha = 1;
                ctx.strokeRect(brick.x, brick.y, brick.width, brick.height);
            });

            // 绘制道具
            powerups.forEach(powerup => {
                ctx.fillStyle = powerup.color;
                ctx.fillRect(powerup.x - 4, powerup.y - 4, 8, 8);
            });

            // 绘制粒子
            particles.forEach(p => {
                ctx.fillStyle = p.color;
                ctx.globalAlpha = p.life / 30;
                ctx.fillRect(p.x - 1, p.y - 1, 2, 2);
            });
            ctx.globalAlpha = 1;

            // 绘制球轨迹
            ball.trail.forEach((t, i) => {
                ctx.fillStyle = colors.trail;
                ctx.globalAlpha = (i + 1) / 5 * 0.5;
                ctx.fillRect(t.x - 1, t.y - 1, 2, 2);
            });
            ctx.globalAlpha = 1;

            // 绘制球
            ctx.fillStyle = colors.ball;
            ctx.beginPath();
            ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制挡板
            ctx.fillStyle = colors.paddle;
            ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);

            // 绘制AI预测线
            if (aiMode && gameState === 'playing') {
                ctx.strokeStyle = '#0f0';
                ctx.lineWidth = 1;
                ctx.setLineDash([2, 2]);
                ctx.beginPath();
                ctx.moveTo(ball.x, ball.y);
                const targetX = ai.predictBall();
                ctx.lineTo(targetX, paddle.y);
                ctx.stroke();
                ctx.setLineDash([]);
            }
        }

        function gameLoop() {
            update();
            render();
            requestAnimationFrame(gameLoop);
        }

        // 游戏控制
        function startGame() {
            if (!audioCtx) initAudio();
            gameState = 'playing';
            document.getElementById('startScreen').classList.add('hidden');
            playSound(440, 0.2, 'triangle');
            createBricks(level);
        }

        function gameOver() {
            gameState = 'gameOver';
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOverScreen').classList.remove('hidden');

            if (score > highScore) {
                highScore = score;
                localStorage.setItem('pixelPongHighScore', highScore);
            }

            playSound(110, 1, 'sawtooth');
            playSound(87.3, 1, 'sawtooth');
        }

        function restartGame() {
            score = 0;
            lives = 3;
            level = 1;
            gameState = 'playing';
            document.getElementById('gameOverScreen').classList.add('hidden');
            resetBall();
            createBricks(level);
            playSound(440, 0.3, 'triangle');
        }

        function nextLevel() {
            level++;
            if (level > levels.length) {
                level = 1;
                // 胜利逻辑
            }
            createBricks(level);
            resetBall();
            playSound(660, 0.3, 'triangle');
            playSound(880, 0.3, 'triangle');
        }

        function resetBall() {
            ball.x = 128;
            ball.y = 200;
            ball.vx = (Math.random() - 0.5) * 4;
            ball.vy = -2;
            ball.trail = [];
        }

        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('highScore').textContent = highScore;
            document.getElementById('lives').textContent = lives;
            document.getElementById('level').textContent = level;
        }

        function toggleAI() {
            aiMode = !aiMode;
            document.getElementById('aiStatus').textContent = aiMode ? '开启' : '关闭';
            document.getElementById('aiIndicator').classList.toggle('hidden', !aiMode);
            playSound(330, 0.1, 'square');
        }

        function toggleSound() {
            soundEnabled = !soundEnabled;
            document.getElementById('soundStatus').textContent = soundEnabled ? '开启' : '关闭';
            playSound(440, 0.1, 'square');
        }

        // 输入处理
        const keys = {};
        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;

            if (e.code === 'Space' && gameState === 'playing') {
                e.preventDefault();
                if (Math.abs(ball.vx) < 0.1 && Math.abs(ball.vy) < 0.1) {
                    ball.vx = (Math.random() - 0.5) * 4;
                    ball.vy = -2;
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });

        // 鼠标控制
        canvas.addEventListener('mousemove', (e) => {
            if (!aiMode && gameState === 'playing') {
                const rect = canvas.getBoundingClientRect();
                const x = (e.clientX - rect.left) * (256 / rect.width);
                paddle.x = Math.max(0, Math.min(256 - paddle.width, x - paddle.width / 2));
            }
        });

        // 游戏循环启动
        gameLoop();
        updateUI();
    </script>
</body>

</html>