<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>像素弹球大师</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #111;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: 'Courier New', monospace;
            color: #fff;
        }

        #gameContainer {
            position: relative;
            width: 512px;
            height: 480px;
            background: #000;
            border: 4px solid #444;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        #gameCanvas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            image-rendering: pixelated;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 20px;
        }

        button {
            background: #333;
            color: #fff;
            border: 2px solid #666;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #444;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
    </div>
    <div class="controls">
        <button id="aiToggle">切换AI模式</button>
        <button id="soundToggle">音效: 开</button>
    </div>

<script>
// 游戏常量
const COLORS = {
    background: '#000000',
    paddle: '#ffffff',
    ball: '#ffffff',
    brick: {
        normal: ['#ff0000', '#00ff00', '#0000ff', '#ffff00'],
        hard: '#888888',
        special: '#ff00ff'
    },
    ui: '#ffffff'
};

// 游戏类
class PixelBreakout {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 设置画布大小为标准红白机分辨率的2倍
        this.canvas.width = 512;
        this.canvas.height = 480;
        
        // 游戏状态
        this.gameState = {
            score: 0,
            lives: 3,
            level: 1,
            isAIMode: true,
            isPaused: false,
            soundEnabled: true
        };

        // 初始化游戏对象
        this.paddle = {
            x: this.canvas.width / 2,
            y: this.canvas.height - 40,
            width: 80,
            height: 10,
            speed: 8
        };

        this.ball = {
            x: this.canvas.width / 2,
            y: this.canvas.height - 60,
            radius: 4,
            speed: 6,
            dx: 0,
            dy: 0,
            isLaunched: false
        };

        // 初始化音频系统
        this.initAudio();
        
        // 初始化砖块
        this.initBricks();
        
        // 绑定控制事件
        this.bindControls();
        
        // 启动游戏循环
        this.gameLoop();
    }

    // 初始化音频系统
    initAudio() {
        this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        
        // 创建主音量控制
        this.masterGain = this.audioCtx.createGain();
        this.masterGain.connect(this.audioCtx.destination);
    }

    // 生成音效
    playSound(type) {
        if (!this.gameState.soundEnabled) return;

        const oscillator = this.audioCtx.createOscillator();
        const gainNode = this.audioCtx.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.masterGain);

        switch(type) {
            case 'paddle':
                oscillator.type = 'square';
                oscillator.frequency.setValueAtTime(220, this.audioCtx.currentTime);
                gainNode.gain.setValueAtTime(0.1, this.audioCtx.currentTime);
                oscillator.start();
                oscillator.stop(this.audioCtx.currentTime + 0.1);
                break;
            case 'brick':
                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(440, this.audioCtx.currentTime);
                gainNode.gain.setValueAtTime(0.1, this.audioCtx.currentTime);
                oscillator.start();
                oscillator.stop(this.audioCtx.currentTime + 0.1);
                break;
            // 更多音效类型...
        }
    }

    // 初始化砖块
    initBricks() {
        this.bricks = [];
        const rows = 5;
        const cols = 8;
        const brickWidth = (this.canvas.width - 40) / cols;
        const brickHeight = 20;

        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                this.bricks.push({
                    x: 20 + j * brickWidth,
                    y: 40 + i * (brickHeight + 5),
                    width: brickWidth - 4,
                    height: brickHeight,
                    color: COLORS.brick.normal[i % COLORS.brick.normal.length],
                    health: 1,
                    isSpecial: Math.random() < 0.1
                });
            }
        }
    }

    // 绑定控制事件
    bindControls() {
        // 键盘状态跟踪
        this.keys = {
            ArrowLeft: false,
            ArrowRight: false
        };

        // 键盘按下事件
        document.addEventListener('keydown', (e) => {
            if (this.gameState.isAIMode) return;
            
            if (e.key in this.keys) {
                this.keys[e.key] = true;
            } else if (e.key === ' ') {
                if (!this.ball.isLaunched) {
                    this.launchBall();
                }
            }
        });

        // 键盘释放事件
        document.addEventListener('keyup', (e) => {
            if (e.key in this.keys) {
                this.keys[e.key] = false;
            }
        });

        // AI切换按钮
        document.getElementById('aiToggle').addEventListener('click', () => {
            this.gameState.isAIMode = !this.gameState.isAIMode;
        });

        // 音效切换按钮
        document.getElementById('soundToggle').addEventListener('click', () => {
            this.gameState.soundEnabled = !this.gameState.soundEnabled;
            document.getElementById('soundToggle').textContent = 
                `音效: ${this.gameState.soundEnabled ? '开' : '关'}`;
        });
    }

    // 发射球
    launchBall() {
        if (!this.ball.isLaunched) {
            this.ball.isLaunched = true;
            this.ball.dx = 2;
            this.ball.dy = -this.ball.speed;
        }
    }

    // AI控制逻辑
    updateAI() {
        if (!this.gameState.isAIMode || !this.ball.isLaunched) return;

        // 预测球的落点
        let predictedX = this.ball.x;
        let tempY = this.ball.y;
        let tempDx = this.ball.dx;
        let tempDy = this.ball.dy;
        let maxBounces = 5; // 限制最大预测反弹次数
        let bounceCount = 0;

        while (tempY < this.paddle.y && bounceCount < maxBounces) {
            // 检查下一个位置
            let nextX = predictedX + tempDx;
            let nextY = tempY + tempDy;

            // 检查砖块碰撞
            let willHitBrick = false;
            for (let brick of this.bricks) {
                if (this.checkPointInBrick(nextX, nextY, brick)) {
                    willHitBrick = true;
                    break;
                }
            }

            // 如果会击中砖块，改变方向
            if (willHitBrick) {
                tempDy = -tempDy;
                bounceCount++;
                continue;
            }

            // 更新位置
            predictedX = nextX;
            tempY = nextY;

            // 检查墙壁碰撞
            if (predictedX < 0 || predictedX > this.canvas.width) {
                tempDx = -tempDx;
                bounceCount++;
            }
            if (tempY < 0) {
                tempDy = -tempDy;
                bounceCount++;
            }
        }

        // 添加动态误差，根据球速调整
        const errorFactor = Math.min(1, this.ball.speed / 10);
        predictedX += (Math.random() - 0.5) * 20 * errorFactor;

        // 移动挡板到预测位置，使用平滑插值
        const targetX = predictedX - this.paddle.width / 2;
        const dx = targetX - this.paddle.x;
        const smoothing = 0.15; // 平滑系数
        
        this.paddle.x += dx * smoothing;
        this.paddle.x = Math.max(0, Math.min(this.canvas.width - this.paddle.width, this.paddle.x));

        // AI模式下自动发射球
        if (!this.ball.isLaunched) {
            this.launchBall();
        }
    }

    // 辅助函数：检查点是否在砖块内
    checkPointInBrick(x, y, brick) {
        return x >= brick.x && 
               x <= brick.x + brick.width && 
               y >= brick.y && 
               y <= brick.y + brick.height;
    }

    // 更新游戏状态
    update() {
        if (this.gameState.isPaused) return;

        this.updateAI();

        // 处理键盘输入
        if (!this.gameState.isAIMode) {
            if (this.keys.ArrowLeft) {
                this.paddle.x = Math.max(0, this.paddle.x - this.paddle.speed);
            }
            if (this.keys.ArrowRight) {
                this.paddle.x = Math.min(this.canvas.width - this.paddle.width, 
                                       this.paddle.x + this.paddle.speed);
            }
        }

        if (!this.ball.isLaunched) {
            this.ball.x = this.paddle.x + this.paddle.width / 2;
            this.ball.y = this.paddle.y - 10;
            return;
        }

        // 更新球的位置
        this.ball.x += this.ball.dx;
        this.ball.y += this.ball.dy;

        // 检查墙壁碰撞
        if (this.ball.x < this.ball.radius || this.ball.x > this.canvas.width - this.ball.radius) {
            this.ball.dx = -this.ball.dx;
            this.playSound('wall');
        }
        if (this.ball.y < this.ball.radius) {
            this.ball.dy = -this.ball.dy;
            this.playSound('wall');
        }

        // 检查挡板碰撞
        if (this.ball.y + this.ball.radius > this.paddle.y &&
            this.ball.x > this.paddle.x &&
            this.ball.x < this.paddle.x + this.paddle.width) {
            
            // 根据击中挡板的位置计算反弹角度
            const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width;
            this.ball.dx = this.ball.speed * (hitPos - 0.5) * 2;
            this.ball.dy = -Math.sqrt(this.ball.speed * this.ball.speed - this.ball.dx * this.ball.dx);
            
            this.playSound('paddle');
        }

        // 检查球是否掉落
        if (this.ball.y > this.canvas.height + this.ball.radius) {
            this.gameState.lives--;
            if (this.gameState.lives <= 0) {
                this.gameOver();
            } else {
                this.resetBall();
            }
        }

        // 检查砖块碰撞
        this.bricks.forEach((brick, index) => {
            if (this.checkBrickCollision(brick)) {
                if (brick.health > 0) {
                    brick.health--;
                    if (brick.health <= 0) {
                        this.gameState.score += brick.isSpecial ? 100 : 50;
                        this.bricks.splice(index, 1);
                    }
                    this.playSound('brick');
                }
            }
        });

        // 检查是否通关
        if (this.bricks.length === 0) {
            this.nextLevel();
        }
    }

    // 检查砖块碰撞
    checkBrickCollision(brick) {
        const ballLeft = this.ball.x - this.ball.radius;
        const ballRight = this.ball.x + this.ball.radius;
        const ballTop = this.ball.y - this.ball.radius;
        const ballBottom = this.ball.y + this.ball.radius;

        if (ballRight > brick.x && 
            ballLeft < brick.x + brick.width &&
            ballBottom > brick.y && 
            ballTop < brick.y + brick.height) {
            
            // 确定碰撞面并改变球的方向
            const fromLeft = ballRight - brick.x;
            const fromRight = (brick.x + brick.width) - ballLeft;
            const fromTop = ballBottom - brick.y;
            const fromBottom = (brick.y + brick.height) - ballTop;

            const min = Math.min(fromLeft, fromRight, fromTop, fromBottom);

            if (min === fromLeft || min === fromRight) {
                this.ball.dx = -this.ball.dx;
            } else {
                this.ball.dy = -this.ball.dy;
            }

            return true;
        }
        return false;
    }

    // 重置球的位置
    resetBall() {
        this.ball.isLaunched = false;
        this.ball.x = this.paddle.x + this.paddle.width / 2;
        this.ball.y = this.paddle.y - 10;
        this.ball.dx = 0;
        this.ball.dy = 0;
    }

    // 进入下一关
    nextLevel() {
        this.gameState.level++;
        this.resetBall();
        this.initBricks();
        // 增加难度
        this.ball.speed += 0.5;
    }

    // 游戏结束
    gameOver() {
        this.gameState.isPaused = true;
        alert(`游戏结束！得分：${this.gameState.score}`);
        // 重置游戏
        this.gameState.score = 0;
        this.gameState.lives = 3;
        this.gameState.level = 1;
        this.ball.speed = 6;
        this.resetBall();
        this.initBricks();
        this.gameState.isPaused = false;
    }

    // 绘制游戏画面
    draw() {
        // 清空画布
        this.ctx.fillStyle = COLORS.background;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制挡板
        this.ctx.fillStyle = COLORS.paddle;
        this.ctx.fillRect(this.paddle.x, this.paddle.y, 
                         this.paddle.width, this.paddle.height);

        // 绘制球
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
        this.ctx.fillStyle = COLORS.ball;
        this.ctx.fill();
        this.ctx.closePath();

        // 绘制砖块
        this.bricks.forEach(brick => {
            this.ctx.fillStyle = brick.isSpecial ? COLORS.brick.special : brick.color;
            this.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);
        });

        // 绘制UI
        this.ctx.fillStyle = COLORS.ui;
        this.ctx.font = '16px "Courier New"';
        this.ctx.fillText(`分数: ${this.gameState.score}`, 10, 20);
        this.ctx.fillText(`生命: ${this.gameState.lives}`, 150, 20);
        this.ctx.fillText(`关卡: ${this.gameState.level}`, 290, 20);
        this.ctx.fillText(`${this.gameState.isAIMode ? 'AI模式' : '玩家模式'}`, 
                         this.canvas.width - 100, 20);

        // AI模式下显示预测轨迹
        if (this.gameState.isAIMode && this.ball.isLaunched) {
            this.drawPrediction();
        }
    }

    // 绘制AI预测轨迹
    drawPrediction() {
        this.ctx.beginPath();
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.setLineDash([5, 5]);
        
        let x = this.ball.x;
        let y = this.ball.y;
        let dx = this.ball.dx;
        let dy = this.ball.dy;
        
        for (let i = 0; i < 50; i++) {
            const nextX = x + dx;
            const nextY = y + dy;
            
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(nextX, nextY);
            
            if (nextX < 0 || nextX > this.canvas.width) dx = -dx;
            if (nextY < 0) dy = -dy;
            if (nextY > this.paddle.y) break;
            
            x = nextX;
            y = nextY;
        }
        
        this.ctx.stroke();
        this.ctx.setLineDash([]);
    }

    // 游戏主循环
    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
window.onload = () => {
    new PixelBreakout();
};
</script>
</body>
</html> 