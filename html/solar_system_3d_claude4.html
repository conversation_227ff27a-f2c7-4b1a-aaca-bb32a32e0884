<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 太阳系运行图</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            color: white;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        /* 控制面板样式 */
        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #333;
            z-index: 100;
            backdrop-filter: blur(10px);
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #FFD700;
            text-align: center;
        }
        
        .speed-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .speed-btn {
            padding: 8px 16px;
            background: #1a1a1a;
            border: 2px solid #333;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .speed-btn:hover {
            background: #333;
            border-color: #555;
        }
        
        .speed-btn.active {
            background: #FFD700;
            color: #000;
            border-color: #FFD700;
        }
        
        .toggle-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .toggle-btn {
            padding: 8px 16px;
            background: #1a1a1a;
            border: 2px solid #333;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .toggle-btn:hover {
            background: #333;
        }
        
        .toggle-btn.active {
            background: #4CAF50;
            border-color: #4CAF50;
        }
        
        /* 信息面板 */
        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #333;
            z-index: 100;
            backdrop-filter: blur(10px);
            max-width: 300px;
            display: none;
        }
        
        .info-panel h4 {
            margin: 0 0 10px 0;
            color: #FFD700;
        }
        
        .info-panel p {
            margin: 5px 0;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🌌 太阳系控制台</h3>
            <div class="speed-controls">
                <button class="speed-btn" data-speed="0.5">慢速</button>
                <button class="speed-btn active" data-speed="1">中速</button>
                <button class="speed-btn" data-speed="2">快速</button>
            </div>
            <div class="toggle-controls">
                <button class="toggle-btn active" id="orbits-toggle">显示轨道</button>
                <button class="toggle-btn active" id="labels-toggle">显示标签</button>
                <button class="toggle-btn" id="info-toggle">星球信息</button>
            </div>
        </div>
        
        <!-- 信息面板 -->
        <div class="info-panel" id="info-panel">
            <h4 id="planet-name">选择一个星球</h4>
            <p id="planet-info">点击任意星球查看详细信息</p>
        </div>
    </div>

    <script>
        // 全局变量
        let scene, camera, renderer, sun, planets = [], orbits = [], labels = [];
        let animationSpeed = 1;
        let showOrbits = true;
        let showLabels = true;
        let showInfo = false;
        let cameraIntroActive = true;
        let introStartTime = Date.now();
        
        // 星球数据
        const planetData = {
            mercury: { 
                name: '水星', 
                distance: 8, 
                size: 1.2, 
                color: 0x8C7853, 
                speed: 0.024,
                info: '距离太阳最近的行星，表面温度极高，几乎没有大气层。'
            },
            venus: { 
                name: '金星', 
                distance: 12, 
                size: 1.5, 
                color: 0xFFC649, 
                speed: 0.015,
                info: '被称为"启明星"，拥有浓厚的大气层，表面温度比水星还高。'
            },
            earth: { 
                name: '地球', 
                distance: 16, 
                size: 1.5, 
                color: 0x6B93D6, 
                speed: 0.01,
                info: '我们美丽的蓝色家园，目前已知唯一存在生命的星球。'
            },
            mars: { 
                name: '火星', 
                distance: 20, 
                size: 1.3, 
                color: 0xCD5C5C, 
                speed: 0.008,
                info: '红色星球，拥有两个小卫星，被认为是最有可能存在生命的星球。'
            },
            jupiter: { 
                name: '木星', 
                distance: 28, 
                size: 3.5, 
                color: 0xD8CA9D, 
                speed: 0.005,
                info: '太阳系最大的行星，拥有众多卫星，包括四颗伽利略卫星。'
            },
            saturn: { 
                name: '土星', 
                distance: 36, 
                size: 3.0, 
                color: 0xFAD5A5, 
                speed: 0.003,
                info: '拥有美丽光环的行星，密度比水还小，如果有足够大的海洋，它会浮起来。'
            },
            uranus: { 
                name: '天王星', 
                distance: 44, 
                size: 2.4, 
                color: 0x4FD0E3, 
                speed: 0.002,
                info: '侧躺着自转的冰巨星，拥有暗淡的光环系统。'
            },
            neptune: { 
                name: '海王星', 
                distance: 52, 
                size: 2.3, 
                color: 0x4B70DD, 
                speed: 0.001,
                info: '太阳系最远的大行星，拥有强烈的风暴和美丽的蓝色外观。'
            },
            pluto: { 
                name: '冥王星', 
                distance: 60, 
                size: 0.8, 
                color: 0x8C7853, 
                speed: 0.0007,
                info: '曾经的第九大行星，现在被归类为矮行星，拥有复杂的轨道。'
            }
        };

        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            // 设置开场相机位置（远离太阳系）
            camera.position.set(0, 50, 300);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000011);
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建星空背景
            createStarField();
            
            // 创建太阳
            createSun();
            
            // 创建行星
            createPlanets();
            
            // 创建轨道线
            createOrbits();
            
            // 创建标签
            createLabels();
            
            // 添加事件监听器
            setupEventListeners();
            
            // 开始动画循环
            animate();
        }
        
        // 创建星空背景
        function createStarField() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({
                color: 0xFFFFFF,
                size: 0.5,
                sizeAttenuation: false
            });
            
            const starsVertices = [];
            for (let i = 0; i < 10000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starsVertices.push(x, y, z);
            }
            
            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const stars = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(stars);
        }
        
        // 创建太阳
        function createSun() {
            const sunGeometry = new THREE.SphereGeometry(5, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700,
                emissive: 0xFFAA00,
                emissiveIntensity: 0.3
            });
            
            sun = new THREE.Mesh(sunGeometry, sunMaterial);
            scene.add(sun);
            
            // 太阳光晕效果
            const glowGeometry = new THREE.SphereGeometry(6, 32, 32);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700,
                transparent: true,
                opacity: 0.2
            });
            const sunGlow = new THREE.Mesh(glowGeometry, glowMaterial);
            scene.add(sunGlow);
        }
        
        // 创建行星
        function createPlanets() {
            Object.keys(planetData).forEach(key => {
                const data = planetData[key];
                const planetGeometry = new THREE.SphereGeometry(data.size, 16, 16);
                const planetMaterial = new THREE.MeshBasicMaterial({ color: data.color });
                const planet = new THREE.Mesh(planetGeometry, planetMaterial);
                
                // 设置初始位置
                planet.position.x = data.distance;
                planet.userData = { 
                    name: key,
                    distance: data.distance,
                    speed: data.speed,
                    angle: Math.random() * Math.PI * 2  // 随机初始角度
                };
                
                scene.add(planet);
                planets.push(planet);
                
                // 为土星添加光环
                if (key === 'saturn') {
                    createSaturnRings(planet);
                }
                
                // 为地球添加月球
                if (key === 'earth') {
                    createMoon(planet);
                }
            });
        }
        
        // 创建土星光环
        function createSaturnRings(saturn) {
            const ringGeometry = new THREE.RingGeometry(3.5, 5.5, 32);
            const ringMaterial = new THREE.MeshBasicMaterial({
                color: 0xA0A0A0,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.6
            });
            const rings = new THREE.Mesh(ringGeometry, ringMaterial);
            rings.rotation.x = Math.PI / 2;
            saturn.add(rings);
        }
        
        // 创建月球
        function createMoon(earth) {
            const moonGeometry = new THREE.SphereGeometry(0.4, 8, 8);
            const moonMaterial = new THREE.MeshBasicMaterial({ color: 0xC0C0C0 });
            const moon = new THREE.Mesh(moonGeometry, moonMaterial);
            moon.position.x = 3.5;
            moon.userData = { 
                speed: 0.1,
                angle: 0,
                distance: 3.5
            };
            earth.add(moon);
        }
        
        // 创建轨道线
        function createOrbits() {
            Object.keys(planetData).forEach(key => {
                const data = planetData[key];
                const orbitGeometry = new THREE.RingGeometry(data.distance - 0.2, data.distance + 0.2, 128);
                const orbitMaterial = new THREE.MeshBasicMaterial({
                    color: 0x666666,
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.6
                });
                const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
                orbit.rotation.x = Math.PI / 2;
                scene.add(orbit);
                orbits.push(orbit);
            });
        }
        
        // 创建标签
        function createLabels() {
            planets.forEach(planet => {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.width = 256;
                canvas.height = 64;
                
                context.fillStyle = 'rgba(0, 0, 0, 0.8)';
                context.fillRect(0, 0, canvas.width, canvas.height);
                
                context.fillStyle = 'white';
                context.font = '20px Arial';
                context.textAlign = 'center';
                context.fillText(planetData[planet.userData.name].name, canvas.width / 2, canvas.height / 2 + 7);
                
                const texture = new THREE.CanvasTexture(canvas);
                const labelMaterial = new THREE.SpriteMaterial({ map: texture });
                const label = new THREE.Sprite(labelMaterial);
                label.scale.set(6, 1.5, 1);
                label.position.y = planet.userData.distance > 20 ? 4 : 3;
                
                planet.add(label);
                labels.push(label);
            });
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);
            
            // 速度控制按钮
            document.querySelectorAll('.speed-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.speed-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    animationSpeed = parseFloat(e.target.dataset.speed);
                });
            });
            
            // 切换按钮
            document.getElementById('orbits-toggle').addEventListener('click', toggleOrbits);
            document.getElementById('labels-toggle').addEventListener('click', toggleLabels);
            document.getElementById('info-toggle').addEventListener('click', toggleInfo);
            
            // 鼠标点击检测
            renderer.domElement.addEventListener('click', onMouseClick);
            
            // 鼠标控制相机
            let mouseDown = false;
            let mouseX = 0, mouseY = 0;
            
            renderer.domElement.addEventListener('mousedown', (e) => {
                mouseDown = true;
                mouseX = e.clientX;
                mouseY = e.clientY;
            });
            
            renderer.domElement.addEventListener('mouseup', () => {
                mouseDown = false;
            });
            
            renderer.domElement.addEventListener('mousemove', (e) => {
                if (!mouseDown || cameraIntroActive) return;
                
                const deltaX = e.clientX - mouseX;
                const deltaY = e.clientY - mouseY;
                
                const spherical = new THREE.Spherical();
                spherical.setFromVector3(camera.position);
                spherical.theta -= deltaX * 0.01;
                spherical.phi += deltaY * 0.01;
                spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
                
                camera.position.setFromSpherical(spherical);
                camera.lookAt(0, 0, 0);
                
                mouseX = e.clientX;
                mouseY = e.clientY;
            });
            
            // 鼠标滚轮缩放
            renderer.domElement.addEventListener('wheel', (e) => {
                if (cameraIntroActive) return;
                const distance = camera.position.length();
                const newDistance = distance + e.deltaY * 0.01;
                camera.position.normalize().multiplyScalar(Math.max(10, Math.min(200, newDistance)));
            });
        }
        
        // 切换轨道显示
        function toggleOrbits() {
            const btn = document.getElementById('orbits-toggle');
            showOrbits = !showOrbits;
            btn.classList.toggle('active');
            orbits.forEach(orbit => orbit.visible = showOrbits);
        }
        
        // 切换标签显示
        function toggleLabels() {
            const btn = document.getElementById('labels-toggle');
            showLabels = !showLabels;
            btn.classList.toggle('active');
            labels.forEach(label => label.visible = showLabels);
        }
        
        // 切换信息面板
        function toggleInfo() {
            const btn = document.getElementById('info-toggle');
            showInfo = !showInfo;
            btn.classList.toggle('active');
            document.getElementById('info-panel').style.display = showInfo ? 'block' : 'none';
        }
        
        // 鼠标点击检测
        function onMouseClick(event) {
            if (!showInfo) return;
            
            const mouse = new THREE.Vector2();
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            
            const raycaster = new THREE.Raycaster();
            raycaster.setFromCamera(mouse, camera);
            
            const intersects = raycaster.intersectObjects(planets);
            if (intersects.length > 0) {
                const planet = intersects[0].object;
                const data = planetData[planet.userData.name];
                document.getElementById('planet-name').textContent = data.name;
                document.getElementById('planet-info').textContent = data.info;
            }
        }
        
        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // 相机开场动画
        function updateCameraIntro() {
            if (!cameraIntroActive) return;
            
            const elapsed = Date.now() - introStartTime;
            const duration = 4000; // 4秒过渡
            
            if (elapsed < duration) {
                const progress = elapsed / duration;
                const easeProgress = 1 - Math.pow(1 - progress, 3); // 缓动函数
                
                // 从远处向太阳系中心移动
                const startPos = new THREE.Vector3(0, 50, 300);
                const endPos = new THREE.Vector3(0, 30, 80);
                
                camera.position.lerpVectors(startPos, endPos, easeProgress);
                camera.lookAt(0, 0, 0);
            } else {
                cameraIntroActive = false;
            }
        }
        
        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新相机开场动画
            updateCameraIntro();
            
            // 太阳自转和脉动效果
            sun.rotation.y += 0.01 * animationSpeed;
            const pulseFactor = 1 + Math.sin(Date.now() * 0.005) * 0.05;
            sun.scale.setScalar(pulseFactor);
            
            // 行星运动
            planets.forEach(planet => {
                const userData = planet.userData;
                userData.angle += userData.speed * animationSpeed;
                
                planet.position.x = Math.cos(userData.angle) * userData.distance;
                planet.position.z = Math.sin(userData.angle) * userData.distance;
                
                // 行星自转
                planet.rotation.y += 0.02 * animationSpeed;
                
                // 月球绕地球运动
                if (userData.name === 'earth') {
                    const moon = planet.children.find(child => child.geometry && child.geometry.type === 'SphereGeometry' && child !== planet);
                    if (moon && moon.userData) {
                        moon.userData.angle += moon.userData.speed * animationSpeed;
                        moon.position.x = Math.cos(moon.userData.angle) * moon.userData.distance;
                        moon.position.z = Math.sin(moon.userData.angle) * moon.userData.distance;
                    }
                }
            });
            
            renderer.render(scene, camera);
        }
        
        // 启动应用
        init();
    </script>
</body>
</html>