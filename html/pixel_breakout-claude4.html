<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>像素弹球大师</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #fff;
        }

        .game-container {
            background: #222;
            border: 4px solid #444;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .game-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .game-title {
            font-size: 24px;
            color: #00ff00;
            text-shadow: 2px 2px 0px #004400;
            margin: 0;
        }

        .game-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .stat-item {
            color: #ffff00;
        }

        canvas {
            border: 2px solid #666;
            background: #000;
            display: block;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .controls {
            margin-top: 15px;
            text-align: center;
        }

        .btn {
            background: #333;
            color: #fff;
            border: 2px solid #666;
            padding: 8px 16px;
            margin: 0 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .btn:hover {
            background: #555;
            border-color: #888;
        }

        .btn.active {
            background: #006600;
            border-color: #00aa00;
            color: #00ff00;
        }

        .instructions {
            margin-top: 10px;
            font-size: 12px;
            color: #aaa;
            text-align: center;
            line-height: 1.4;
        }

        .ai-indicator {
            position: relative;
            display: inline-block;
            margin-left: 10px;
        }

        .ai-dot {
            width: 8px;
            height: 8px;
            background: #00ff00;
            border-radius: 50%;
            display: inline-block;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .game-over-screen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #666;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            display: none;
        }

        .level-transition {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #666;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            display: none;
            color: #00ff00;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">像素弹球大师</h1>
        </div>
        
        <div class="game-stats">
            <div class="stat-item">分数: <span id="score">0</span></div>
            <div class="stat-item">最高分: <span id="highScore">0</span></div>
            <div class="stat-item">生命: <span id="lives">3</span></div>
            <div class="stat-item">关卡: <span id="level">1</span></div>
            <div class="stat-item">
                模式: <span id="mode">AI演示</span>
                <span class="ai-indicator" id="aiIndicator">
                    <span class="ai-dot"></span>
                </span>
            </div>
        </div>

        <div style="position: relative;">
            <canvas id="gameCanvas" width="512" height="480"></canvas>
            
            <div class="game-over-screen" id="gameOverScreen">
                <h2 id="gameOverTitle">游戏结束</h2>
                <p id="gameOverMessage">最终分数: <span id="finalScore">0</span></p>
                <button class="btn" onclick="restartGame()">重新开始</button>
            </div>

            <div class="level-transition" id="levelTransition">
                <h2>关卡完成!</h2>
                <p id="levelMessage">准备进入关卡 2</p>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="aiModeBtn" onclick="toggleAIMode()">切换到人类模式</button>
            <button class="btn" onclick="toggleSound()">音效: <span id="soundStatus">开启</span></button>
            <button class="btn" onclick="pauseGame()">暂停</button>
            <button class="btn" onclick="restartGame()">重新开始</button>
        </div>

        <div class="instructions">
            <div>AI模式: 观看AI自动演示游戏</div>
            <div>人类模式: 方向键或鼠标控制挡板，空格发球</div>
            <div>目标: 击破所有砖块，接住掉落的道具</div>
        </div>
    </div>

    <script>
        // 游戏配置
        const GAME_CONFIG = {
            CANVAS_WIDTH: 512,
            CANVAS_HEIGHT: 480,
            PADDLE_WIDTH: 60,
            PADDLE_HEIGHT: 8,
            BALL_SIZE: 4,
            BRICK_WIDTH: 32,
            BRICK_HEIGHT: 16,
            BRICK_PADDING: 2,
            BRICK_OFFSET_TOP: 60,
            BRICK_OFFSET_LEFT: 30,
            COLORS: {
                PADDLE: '#00ff00',
                BALL: '#ffffff',
                BRICK_NORMAL: '#ff0000',
                BRICK_STRONG: '#0000ff',
                BRICK_SPECIAL: '#ffff00',
                POWERUP: '#ff00ff',
                WALL: '#666666',
                PREDICTION: '#00ffff'
            }
        };

        // 游戏变量
        let canvas, ctx, gameLoop;
        let gameState = {
            score: 0,
            highScore: parseInt(localStorage.getItem('pixelBreakoutHighScore')) || 0,
            lives: 3,
            level: 1,
            gameRunning: false,
            gamePaused: false,
            aiMode: true,
            soundEnabled: true,
            showPrediction: true
        };

        // 游戏对象
        let paddle, ball, bricks = [], powerups = [];
        let keys = {};
        let mouse = { x: 0, y: 0 };

        // 音效系统
        let audioContext;
        let masterGain;

        // 初始化音效系统
        function initAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                masterGain = audioContext.createGain();
                masterGain.connect(audioContext.destination);
                masterGain.gain.value = 0.3;
            } catch (e) {
                console.warn('Web Audio API not supported');
                gameState.soundEnabled = false;
            }
        }

        // 播放音效函数
        function playSound(type, frequency = 440, duration = 0.1, volume = 0.5) {
            if (!gameState.soundEnabled || !audioContext) return;

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(masterGain);

            switch (type) {
                case 'paddle':
                    oscillator.type = 'square';
                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                    break;
                
                case 'brick':
                    oscillator.type = 'sawtooth';
                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.5, audioContext.currentTime + duration);
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                    break;
                
                case 'powerup':
                    oscillator.type = 'sine';
                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(frequency * 2, audioContext.currentTime + duration);
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                    break;
                
                case 'wall':
                    oscillator.type = 'triangle';
                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                    break;
                
                case 'lose':
                    oscillator.type = 'sawtooth';
                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.2, audioContext.currentTime + duration);
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                    break;
            }

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // 播放胜利音效
        function playVictorySound() {
            if (!gameState.soundEnabled || !audioContext) return;

            const notes = [262, 330, 392, 523]; // C4, E4, G4, C5
            notes.forEach((freq, i) => {
                setTimeout(() => {
                    playSound('powerup', freq, 0.2, 0.4);
                }, i * 150);
            });
        }

        // 播放背景音乐
        function playBackgroundMusic() {
            if (!gameState.soundEnabled || !audioContext) return;

            const melody = [392, 523, 659, 523, 392, 330, 262, 330];
            let noteIndex = 0;

            function playNextNote() {
                if (gameState.gameRunning && gameState.soundEnabled) {
                    playSound('powerup', melody[noteIndex], 0.3, 0.1);
                    noteIndex = (noteIndex + 1) % melody.length;
                    setTimeout(playNextNote, 800);
                }
            }
            
            setTimeout(playNextNote, 1000);
        }

        // 挡板类
        class Paddle {
            constructor() {
                this.width = GAME_CONFIG.PADDLE_WIDTH;
                this.height = GAME_CONFIG.PADDLE_HEIGHT;
                this.x = (GAME_CONFIG.CANVAS_WIDTH - this.width) / 2;
                this.y = GAME_CONFIG.CANVAS_HEIGHT - 30;
                this.speed = 6;
                this.targetX = this.x; // AI目标位置
            }

            update() {
                if (gameState.aiMode) {
                    this.updateAI();
                } else {
                    this.updateHuman();
                }

                // 限制挡板在游戏区域内
                this.x = Math.max(0, Math.min(GAME_CONFIG.CANVAS_WIDTH - this.width, this.x));
            }

            updateAI() {
                // AI预测球的落点
                let prediction = this.predictBallPosition();
                
                if (prediction.x !== null) {
                    this.targetX = prediction.x - this.width / 2;
                    
                    // 添加一些人类化的不准确性
                    if (Math.random() < 0.1) {
                        this.targetX += (Math.random() - 0.5) * 20;
                    }
                }

                // 平滑移动到目标位置
                let diff = this.targetX - this.x;
                if (Math.abs(diff) > 2) {
                    this.x += Math.sign(diff) * this.speed * 0.8;
                }
            }

            updateHuman() {
                if (keys['ArrowLeft'] || keys['a']) {
                    this.x -= this.speed;
                }
                if (keys['ArrowRight'] || keys['d']) {
                    this.x += this.speed;
                }
            }

            predictBallPosition() {
                if (!ball) return { x: null, y: null };

                let tempX = ball.x;
                let tempY = ball.y;
                let tempDx = ball.dx;
                let tempDy = ball.dy;

                // 如果球正在向上移动，不需要预测
                if (tempDy < 0) {
                    return { x: null, y: null };
                }

                // 模拟球的运动直到它到达挡板高度
                let steps = 0;
                const maxSteps = 500;

                while (tempY < this.y && steps < maxSteps) {
                    tempX += tempDx;
                    tempY += tempDy;

                    // 检查左右边界反弹
                    if (tempX <= GAME_CONFIG.BALL_SIZE || tempX >= GAME_CONFIG.CANVAS_WIDTH - GAME_CONFIG.BALL_SIZE) {
                        tempDx = -tempDx;
                    }

                    steps++;
                }

                return { x: tempX, y: tempY };
            }

            draw() {
                ctx.fillStyle = GAME_CONFIG.COLORS.PADDLE;
                ctx.fillRect(this.x, this.y, this.width, this.height);

                // 绘制AI预测轨迹
                if (gameState.aiMode && gameState.showPrediction) {
                    let prediction = this.predictBallPosition();
                    if (prediction.x !== null) {
                        ctx.fillStyle = GAME_CONFIG.COLORS.PREDICTION;
                        ctx.fillRect(prediction.x - 2, this.y - 4, 4, 2);
                    }
                }
            }
        }

        // 球类
        class Ball {
            constructor() {
                this.size = GAME_CONFIG.BALL_SIZE;
                this.reset();
            }

            reset() {
                this.x = GAME_CONFIG.CANVAS_WIDTH / 2;
                this.y = GAME_CONFIG.CANVAS_HEIGHT - 50;
                this.dx = (Math.random() - 0.5) * 4;
                this.dy = -4;
                this.speed = 4;
                this.stuck = true;
            }

            update() {
                if (this.stuck) {
                    this.x = paddle.x + paddle.width / 2;
                    if (keys[' '] || gameState.aiMode) {
                        this.stuck = false;
                        this.dx = (Math.random() - 0.5) * 4;
                        this.dy = -4;
                    }
                    return;
                }

                this.x += this.dx;
                this.y += this.dy;

                // 墙壁碰撞
                if (this.x <= this.size || this.x >= GAME_CONFIG.CANVAS_WIDTH - this.size) {
                    this.dx = -this.dx;
                    playSound('wall', 220, 0.1, 0.3);
                }

                if (this.y <= this.size) {
                    this.dy = -this.dy;
                    playSound('wall', 220, 0.1, 0.3);
                }

                // 挡板碰撞
                if (this.y + this.size >= paddle.y && 
                    this.x >= paddle.x && 
                    this.x <= paddle.x + paddle.width) {
                    
                    let hitPos = (this.x - paddle.x) / paddle.width;
                    this.dx = (hitPos - 0.5) * 8;
                    this.dy = -Math.abs(this.dy);
                    
                    playSound('paddle', 330, 0.1, 0.4);
                }

                // 砖块碰撞
                this.checkBrickCollision();

                // 球掉落
                if (this.y > GAME_CONFIG.CANVAS_HEIGHT) {
                    this.loseLife();
                }
            }

            checkBrickCollision() {
                for (let i = bricks.length - 1; i >= 0; i--) {
                    let brick = bricks[i];
                    if (this.x >= brick.x && this.x <= brick.x + brick.width &&
                        this.y >= brick.y && this.y <= brick.y + brick.height) {
                        
                        // 确定碰撞面
                        let overlapLeft = (this.x + this.size) - brick.x;
                        let overlapRight = (brick.x + brick.width) - (this.x - this.size);
                        let overlapTop = (this.y + this.size) - brick.y;
                        let overlapBottom = (brick.y + brick.height) - (this.y - this.size);

                        let minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

                        if (minOverlap === overlapLeft || minOverlap === overlapRight) {
                            this.dx = -this.dx;
                        } else {
                            this.dy = -this.dy;
                        }

                        brick.hit();
                        break;
                    }
                }
            }

            loseLife() {
                gameState.lives--;
                updateUI();
                
                if (gameState.lives <= 0) {
                    gameOver();
                } else {
                    this.reset();
                    playSound('lose', 150, 0.5, 0.5);
                }
            }

            draw() {
                ctx.fillStyle = GAME_CONFIG.COLORS.BALL;
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
            }
        }

        // 砖块类
        class Brick {
            constructor(x, y, type = 'normal') {
                this.x = x;
                this.y = y;
                this.width = GAME_CONFIG.BRICK_WIDTH;
                this.height = GAME_CONFIG.BRICK_HEIGHT;
                this.type = type;
                this.hits = type === 'strong' ? 2 : 1;
                this.maxHits = this.hits;
                this.color = this.getColor();
                this.points = this.getPoints();
            }

            getColor() {
                switch (this.type) {
                    case 'strong': return GAME_CONFIG.COLORS.BRICK_STRONG;
                    case 'special': return GAME_CONFIG.COLORS.BRICK_SPECIAL;
                    default: return GAME_CONFIG.COLORS.BRICK_NORMAL;
                }
            }

            getPoints() {
                switch (this.type) {
                    case 'strong': return 20;
                    case 'special': return 50;
                    default: return 10;
                }
            }

            hit() {
                this.hits--;
                
                if (this.hits <= 0) {
                    gameState.score += this.points;
                    
                    if (this.type === 'special') {
                        this.dropPowerup();
                    }
                    
                    this.destroy();
                    playSound('brick', 440 + Math.random() * 200, 0.15, 0.3);
                } else {
                    playSound('brick', 220, 0.1, 0.2);
                }
            }

            dropPowerup() {
                if (Math.random() < 0.7) {
                    let types = ['multiball', 'expand', 'slow', 'pierce'];
                    let type = types[Math.floor(Math.random() * types.length)];
                    powerups.push(new Powerup(this.x + this.width/2, this.y + this.height, type));
                }
            }

            destroy() {
                let index = bricks.indexOf(this);
                if (index > -1) {
                    bricks.splice(index, 1);
                }
            }

            draw() {
                let alpha = this.hits / this.maxHits;
                ctx.globalAlpha = alpha;
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                ctx.fillStyle = '#000';
                ctx.fillRect(this.x + 1, this.y + 1, this.width - 2, this.height - 2);
                
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);
                
                ctx.globalAlpha = 1;
            }
        }

        // 道具类
        class Powerup {
            constructor(x, y, type) {
                this.x = x;
                this.y = y;
                this.size = 8;
                this.type = type;
                this.speed = 2;
                this.color = GAME_CONFIG.COLORS.POWERUP;
            }

            update() {
                this.y += this.speed;

                // 检查与挡板碰撞
                if (this.y + this.size >= paddle.y && 
                    this.x >= paddle.x && 
                    this.x <= paddle.x + paddle.width) {
                    this.activate();
                    this.destroy();
                }

                // 移除掉落的道具
                if (this.y > GAME_CONFIG.CANVAS_HEIGHT) {
                    this.destroy();
                }
            }

            activate() {
                playSound('powerup', 660, 0.3, 0.4);
                
                switch (this.type) {
                    case 'expand':
                        paddle.width = Math.min(paddle.width + 20, 120);
                        break;
                    case 'slow':
                        ball.dx *= 0.7;
                        ball.dy *= 0.7;
                        break;
                    // 其他道具效果可以在这里添加
                }
            }

            destroy() {
                let index = powerups.indexOf(this);
                if (index > -1) {
                    powerups.splice(index, 1);
                }
            }

            draw() {
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
            }
        }

        // 关卡配置
        const LEVELS = [
            {
                layout: [
                    ['normal', 'normal', 'special', 'normal', 'normal', 'special', 'normal', 'normal'],
                    ['normal', 'strong', 'normal', 'normal', 'normal', 'normal', 'strong', 'normal'],
                    ['special', 'normal', 'normal', 'strong', 'strong', 'normal', 'normal', 'special'],
                    ['normal', 'normal', 'normal', 'normal', 'normal', 'normal', 'normal', 'normal']
                ]
            },
            {
                layout: [
                    ['strong', 'strong', 'strong', 'strong', 'strong', 'strong', 'strong', 'strong'],
                    ['normal', 'special', 'normal', 'normal', 'normal', 'normal', 'special', 'normal'],
                    ['normal', 'normal', 'strong', 'special', 'special', 'strong', 'normal', 'normal'],
                    ['special', 'normal', 'normal', 'strong', 'strong', 'normal', 'normal', 'special'],
                    ['normal', 'normal', 'normal', 'normal', 'normal', 'normal', 'normal', 'normal']
                ]
            },
            {
                layout: [
                    ['special', 'strong', 'special', 'strong', 'strong', 'special', 'strong', 'special'],
                    ['strong', 'strong', 'strong', 'strong', 'strong', 'strong', 'strong', 'strong'],
                    ['normal', 'special', 'strong', 'special', 'special', 'strong', 'special', 'normal'],
                    ['normal', 'normal', 'strong', 'strong', 'strong', 'strong', 'normal', 'normal'],
                    ['special', 'normal', 'normal', 'strong', 'strong', 'normal', 'normal', 'special']
                ]
            }
        ];

        // 初始化游戏
        function init() {
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            
            initAudio();
            initGameObjects();
            initEventListeners();
            updateUI();
            
            gameState.gameRunning = true;
            gameLoop = setInterval(update, 1000 / 60);
            
            // 开始背景音乐
            playBackgroundMusic();
        }

        function initGameObjects() {
            paddle = new Paddle();
            ball = new Ball();
            createBricks();
        }

        function createBricks() {
            bricks = [];
            let level = LEVELS[(gameState.level - 1) % LEVELS.length];
            
            for (let row = 0; row < level.layout.length; row++) {
                for (let col = 0; col < level.layout[row].length; col++) {
                    if (level.layout[row][col] !== null) {
                        let x = col * (GAME_CONFIG.BRICK_WIDTH + GAME_CONFIG.BRICK_PADDING) + GAME_CONFIG.BRICK_OFFSET_LEFT;
                        let y = row * (GAME_CONFIG.BRICK_HEIGHT + GAME_CONFIG.BRICK_PADDING) + GAME_CONFIG.BRICK_OFFSET_TOP;
                        bricks.push(new Brick(x, y, level.layout[row][col]));
                    }
                }
            }
        }

        function initEventListeners() {
            document.addEventListener('keydown', (e) => {
                keys[e.key] = true;
                if (e.key === ' ') {
                    e.preventDefault();
                }
            });

            document.addEventListener('keyup', (e) => {
                keys[e.key] = false;
            });

            canvas.addEventListener('mousemove', (e) => {
                if (!gameState.aiMode) {
                    let rect = canvas.getBoundingClientRect();
                    mouse.x = e.clientX - rect.left;
                    paddle.x = mouse.x - paddle.width / 2;
                }
            });

            canvas.addEventListener('click', (e) => {
                if (ball.stuck && !gameState.aiMode) {
                    ball.stuck = false;
                    ball.dx = (Math.random() - 0.5) * 4;
                    ball.dy = -4;
                }
            });
        }

        // 游戏主循环
        function update() {
            if (!gameState.gameRunning || gameState.gamePaused) return;

            paddle.update();
            ball.update();
            
            powerups.forEach(powerup => powerup.update());

            // 检查关卡完成
            if (bricks.filter(brick => brick.type !== 'unbreakable').length === 0) {
                nextLevel();
            }

            draw();
            updateUI();
        }

        function draw() {
            ctx.clearRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);

            paddle.draw();
            ball.draw();
            bricks.forEach(brick => brick.draw());
            powerups.forEach(powerup => powerup.draw());

            // 绘制边框
            ctx.strokeStyle = GAME_CONFIG.COLORS.WALL;
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);
        }

        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('highScore').textContent = gameState.highScore;
            document.getElementById('lives').textContent = gameState.lives;
            document.getElementById('level').textContent = gameState.level;
            document.getElementById('mode').textContent = gameState.aiMode ? 'AI演示' : '人类模式';
            document.getElementById('aiIndicator').style.display = gameState.aiMode ? 'inline-block' : 'none';
            document.getElementById('aiModeBtn').textContent = gameState.aiMode ? '切换到人类模式' : '切换到AI模式';
            document.getElementById('aiModeBtn').className = gameState.aiMode ? 'btn active' : 'btn';
            document.getElementById('soundStatus').textContent = gameState.soundEnabled ? '开启' : '关闭';
        }

        function nextLevel() {
            gameState.level++;
            showLevelTransition();
            playVictorySound();
            
            setTimeout(() => {
                createBricks();
                ball.reset();
                paddle.width = GAME_CONFIG.PADDLE_WIDTH; // 重置挡板大小
                hideLevelTransition();
            }, 2000);
        }

        function showLevelTransition() {
            let screen = document.getElementById('levelTransition');
            let message = document.getElementById('levelMessage');
            message.textContent = `准备进入关卡 ${gameState.level}`;
            screen.style.display = 'block';
        }

        function hideLevelTransition() {
            document.getElementById('levelTransition').style.display = 'none';
        }

        function gameOver() {
            gameState.gameRunning = false;
            
            if (gameState.score > gameState.highScore) {
                gameState.highScore = gameState.score;
                localStorage.setItem('pixelBreakoutHighScore', gameState.highScore);
            }
            
            document.getElementById('finalScore').textContent = gameState.score;
            document.getElementById('gameOverScreen').style.display = 'block';
            
            playSound('lose', 100, 1.0, 0.6);
        }

        function restartGame() {
            gameState.score = 0;
            gameState.lives = 3;
            gameState.level = 1;
            gameState.gameRunning = true;
            gameState.gamePaused = false;
            
            document.getElementById('gameOverScreen').style.display = 'none';
            hideLevelTransition();
            
            initGameObjects();
            updateUI();
            
            if (!gameLoop) {
                gameLoop = setInterval(update, 1000 / 60);
            }
            
            // 重新开始背景音乐
            playBackgroundMusic();
        }

        function pauseGame() {
            gameState.gamePaused = !gameState.gamePaused;
            
            if (gameState.gamePaused) {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);
                ctx.fillStyle = '#fff';
                ctx.font = '24px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText('暂停', GAME_CONFIG.CANVAS_WIDTH / 2, GAME_CONFIG.CANVAS_HEIGHT / 2);
            }
        }

        function toggleAIMode() {
            gameState.aiMode = !gameState.aiMode;
            
            if (!gameState.aiMode) {
                // 切换到人类模式时，如果球被卡住，解除卡住状态
                if (ball.stuck) {
                    ball.stuck = false;
                    ball.dx = (Math.random() - 0.5) * 4;
                    ball.dy = -4;
                }
            }
            
            updateUI();
        }

        function toggleSound() {
            gameState.soundEnabled = !gameState.soundEnabled;
            
            if (gameState.soundEnabled) {
                // 重新初始化音频上下文（如果需要）
                if (!audioContext) {
                    initAudio();
                }
                // 恢复背景音乐
                playBackgroundMusic();
            } else if (audioContext) {
                audioContext.suspend();
            }
            
            updateUI();
        }

        // 额外的音效和视觉效果
        function createParticleEffect(x, y, color) {
            // 简单的粒子效果，可以在砖块被击中时显示
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    ctx.fillStyle = color;
                    ctx.globalAlpha = 0.5;
                    ctx.fillRect(
                        x + (Math.random() - 0.5) * 20,
                        y + (Math.random() - 0.5) * 20,
                        2, 2
                    );
                    ctx.globalAlpha = 1;
                }, i * 50);
            }
        }

        // 改进的AI系统
        function improveAI() {
            // 为AI添加更多智能行为
            if (gameState.aiMode && paddle) {
                // 检查是否有道具掉落
                let nearestPowerup = null;
                let nearestDistance = Infinity;
                
                powerups.forEach(powerup => {
                    let distance = Math.abs(powerup.x - (paddle.x + paddle.width / 2));
                    if (distance < nearestDistance && powerup.y > GAME_CONFIG.CANVAS_HEIGHT / 2) {
                        nearestDistance = distance;
                        nearestPowerup = powerup;
                    }
                });
                
                // 如果有近距离道具，优先收集
                if (nearestPowerup && nearestDistance < 100) {
                    paddle.targetX = nearestPowerup.x - paddle.width / 2;
                }
            }
        }

        // 增强的碰撞检测
        function enhancedCollisionDetection() {
            // 可以在这里添加更精确的碰撞检测逻辑
            // 例如：圆形球与矩形的精确碰撞
        }

        // 游戏难度调整
        function adjustDifficulty() {
            // 根据关卡调整游戏难度
            if (gameState.level > 1) {
                // 稍微增加球的速度
                ball.speed = 4 + (gameState.level - 1) * 0.5;
                
                // 减少挡板大小（如果没有道具效果）
                if (paddle.width > 40) {
                    paddle.width = Math.max(40, GAME_CONFIG.PADDLE_WIDTH - (gameState.level - 1) * 5);
                }
            }
        }

        // 改进的关卡过渡效果
        function enhancedLevelTransition() {
            let screen = document.getElementById('levelTransition');
            screen.style.animation = 'fadeIn 0.5s ease-in-out';
            
            setTimeout(() => {
                screen.style.animation = 'fadeOut 0.5s ease-in-out';
            }, 1500);
        }

        // 添加更多键盘快捷键
        function addKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case 'p':
                    case 'P':
                        pauseGame();
                        break;
                    case 'r':
                    case 'R':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            restartGame();
                        }
                        break;
                    case 'm':
                    case 'M':
                        toggleSound();
                        break;
                    case 'Escape':
                        if (!gameState.gamePaused) {
                            pauseGame();
                        }
                        break;
                }
            });
        }

        // 性能优化
        function optimizeRendering() {
            // 使用requestAnimationFrame替代setInterval以获得更好的性能
            function gameLoopRAF() {
                if (gameState.gameRunning) {
                    update();
                    requestAnimationFrame(gameLoopRAF);
                }
            }
            
            // 可以选择使用RAF而不是setInterval
            // requestAnimationFrame(gameLoopRAF);
        }

        // 添加触摸设备支持
        function addTouchSupport() {
            let touchStartX = 0;
            
            canvas.addEventListener('touchstart', (e) => {
                e.preventDefault();
                touchStartX = e.touches[0].clientX;
            });
            
            canvas.addEventListener('touchmove', (e) => {
                e.preventDefault();
                if (!gameState.aiMode) {
                    let rect = canvas.getBoundingClientRect();
                    let touchX = e.touches[0].clientX - rect.left;
                    paddle.x = touchX - paddle.width / 2;
                }
            });
            
            canvas.addEventListener('touchend', (e) => {
                e.preventDefault();
                if (ball.stuck && !gameState.aiMode) {
                    ball.stuck = false;
                    ball.dx = (Math.random() - 0.5) * 4;
                    ball.dy = -4;
                }
            });
        }

        // 保存游戏状态
        function saveGameState() {
            let gameData = {
                score: gameState.score,
                level: gameState.level,
                lives: gameState.lives,
                highScore: gameState.highScore
            };
            localStorage.setItem('pixelBreakoutSave', JSON.stringify(gameData));
        }

        // 加载游戏状态
        function loadGameState() {
            let savedData = localStorage.getItem('pixelBreakoutSave');
            if (savedData) {
                let gameData = JSON.parse(savedData);
                gameState.score = gameData.score || 0;
                gameState.level = gameData.level || 1;
                gameState.lives = gameData.lives || 3;
                gameState.highScore = gameData.highScore || 0;
            }
        }

        // 添加CSS动画
        function addCSSAnimations() {
            let style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; transform: scale(0.8); }
                    to { opacity: 1; transform: scale(1); }
                }
                
                @keyframes fadeOut {
                    from { opacity: 1; transform: scale(1); }
                    to { opacity: 0; transform: scale(0.8); }
                }
                
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-2px); }
                    75% { transform: translateX(2px); }
                }
                
                .game-container {
                    animation: fadeIn 1s ease-in-out;
                }
                
                .btn:active {
                    animation: shake 0.2s ease-in-out;
                }
            `;
            document.head.appendChild(style);
        }

        // 初始化所有增强功能
        function initEnhancements() {
            addKeyboardShortcuts();
            addTouchSupport();
            addCSSAnimations();
            
            // 定期保存游戏状态
            setInterval(saveGameState, 5000);
            
            // 在游戏更新中调用AI改进
            setInterval(() => {
                if (gameState.gameRunning) {
                    improveAI();
                    adjustDifficulty();
                }
            }, 100);
        }

        // 启动游戏
        window.addEventListener('load', () => {
            loadGameState();
            init();
            initEnhancements();
        });

        // 页面卸载时保存状态
        window.addEventListener('beforeunload', () => {
            saveGameState();
        });
    </script>
</body>
</html>