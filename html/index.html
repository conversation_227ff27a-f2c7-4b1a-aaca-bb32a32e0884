<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型作品对比展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            margin-bottom: 20px;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .projects-grid {
            display: grid;
            gap: 60px;
        }

        .project-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .project-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
            text-align: center;
        }

        .project-description {
            text-align: center;
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
        }

        .model-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .model-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .model-header {
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .model-name {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .model-info {
            font-size: 0.9rem;
            opacity: 0.9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-size {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .model-content {
            padding: 25px;
        }

        .preview-btn {
            display: block;
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .preview-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .features-list {
            margin-bottom: 20px;
        }

        .feature-tag {
            display: inline-block;
            background: #f7fafc;
            color: #4a5568;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 4px 4px 4px 0;
            border: 1px solid #e2e8f0;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #666;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-weight: 600;
            color: #2d3748;
            display: block;
            font-size: 1.1rem;
        }

        .model-claude4 .model-header { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .model-claude3 .model-header { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .model-kimi .model-header { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .model-deepseek .model-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .model-chatgpt .model-header { background: linear-gradient(135deg, #96fbc4 0%, #f9f586 100%); }

        .comparison-note {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 30px;
            margin-top: 40px;
            text-align: center;
            border-left: 5px solid #667eea;
        }

        .comparison-note h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .comparison-note p {
            color: #666;
            line-height: 1.6;
        }

        /* 提示词样式 */
        .prompts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 40px;
        }

        .prompt-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }

        .prompt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .prompt-type {
            background: rgba(255,255,255,0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .prompt-content {
            padding: 30px;
        }

        .prompt-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
            line-height: 1.6;
        }

        .prompt-details {
            margin-top: 20px;
        }

        .prompt-details summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            list-style: none;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-details summary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .prompt-details summary::-webkit-details-marker {
            display: none;
        }

        .prompt-text {
            background: #fafafa;
            padding: 30px;
            margin-top: 15px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            line-height: 1.7;
        }

        .prompt-text h4 {
            color: #2d3748;
            margin: 25px 0 15px 0;
            font-size: 1.1rem;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }

        .prompt-text ul, .prompt-text ol {
            margin: 15px 0;
            padding-left: 20px;
        }

        .prompt-text li {
            margin: 8px 0;
        }

        .prompt-text ul ul {
            margin: 8px 0;
        }

        .prompt-text strong {
            color: #2d3748;
        }

        .prompt-text p {
            margin: 15px 0;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .prompts-grid {
                grid-template-columns: 1fr;
            }
            
            .prompt-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .prompt-content {
                padding: 20px;
            }
            
            .prompt-text {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .project-title { font-size: 2rem; }
            .models-grid { grid-template-columns: 1fr; }
            .container { padding: 20px 15px; }
            .project-section { padding: 25px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI模型作品对比展示</h1>
            <p>探索不同AI模型在创建交互式网页应用时的独特风格与能力差异，每个版本都展现了各自模型的创意特色。</p>
        </div>

        <!-- 提示词展示区域 -->
        <div class="project-section">
            <h2 class="project-title">📝 项目提示词</h2>
            <p class="project-description">查看用于生成这些项目的原始提示词，了解AI模型是如何根据详细需求创建出不同风格的作品。</p>
        
            <div class="prompts-grid">
                <!-- 太阳系提示词 -->
                <div class="prompt-card">
                    <div class="prompt-header">
                        <h3>🌌 太阳系运行图提示词</h3>
                        <span class="prompt-type">3D图形编程</span>
                    </div>
                    <div class="prompt-content">
                        <div class="prompt-summary">
                            <strong>项目目标：</strong>使用Three.js创建动态太阳系运行图，包含10个天体的真实轨道模拟
                        </div>
                        <details class="prompt-details">
                            <summary>查看完整提示词 →</summary>
                            <div class="prompt-text">
                                <p>创建一个单一HTML文件，包含CSS和JavaScript，以Three.js生成一个动态的太阳系运行图。这个运行图应该包含以下天体，并具有各自独特的动画效果：</p>
        
                                <h4>天体要求：</h4>
                                <ol>
                                    <li><strong>太阳：</strong>显示在中央，有发光和脉动效果</li>
                                    <li><strong>水星：</strong>最靠近太阳的行星，灰褐色</li>
                                    <li><strong>金星：</strong>第二颗行星，黄白色</li>
                                    <li><strong>地球：</strong>第三颗行星，蓝绿色，可选添加一个绕行的月球</li>
                                    <li><strong>火星：</strong>第四颗行星，红色</li>
                                    <li><strong>木星：</strong>第五颗行星，橙褐色，体积最大</li>
                                    <li><strong>土星：</strong>第六颗行星，黄棕色，具有环带</li>
                                    <li><strong>天王星：</strong>第七颗行星，青蓝色</li>
                                    <li><strong>海王星：</strong>第八颗行星，深蓝色</li>
                                    <li><strong>冥王星：</strong>被开除行星籍的矮行星，灰褐色，体积最小</li>
                                </ol>
        
                                <h4>运行特性：</h4>
                                <ul>
                                    <li>按照相对真实的轨道半径比例围绕太阳运行（可适当调整以适应显示）</li>
                                    <li>具有相对准确的视觉特征（颜色、环等）</li>
                                    <li>以不同的速度运行，模拟真实的轨道周期差异</li>
                                </ul>
        
                                <h4>界面要求：</h4>
                                <ul>
                                    <li>深色背景，模拟太空环境，可添加星星点缀</li>
                                    <li>太阳应有发光效果</li>
                                    <li>使用者可以通过按钮或控制面板调整运行速度（如：慢速、中速、快速）</li>
                                    <li>可选添加行星名称标签和轨道线</li>
                                </ul>
        
                                <p>请提供完整的HTML、CSS和JavaScript代码，使这个太阳系运行图能够在浏览器中直接运行。代码应组织良好，并附有注释，以便于理解和修改。</p>
                            </div>
                        </details>
                    </div>
                </div>
        
                <!-- 弹球游戏提示词 -->
                <div class="prompt-card">
                    <div class="prompt-header">
                        <h3>🎮 像素弹球大师提示词</h3>
                        <span class="prompt-type">游戏开发</span>
                    </div>
                    <div class="prompt-content">
                        <div class="prompt-summary">
                            <strong>项目目标：</strong>创建红白机风格的弹球游戏，包含AI自动演示功能和Web Audio API音效系统
                        </div>
                        <details class="prompt-details">
                            <summary>查看完整提示词 →</summary>
                            <div class="prompt-text">
                                <p>创建一个红白机风格的"像素弹球大师"游戏，包含自动演示AI功能，使用纯HTML/CSS/JavaScript实现为单文件：</p>
        
                                <h4>1. 游戏视觉与核心玩法：</h4>
                                <ul>
                                    <li>复刻经典打砖块/弹球街机游戏，采用8位像素美学</li>
                                    <li>游戏区域使用固定比例（类似256×240像素），周围添加复古装饰</li>
                                    <li>所有元素通过Canvas绘制，使用有限调色板（16-20种颜色）</li>
                                    <li>基础玩法：玩家控制挡板反弹球体，击碎屏幕上方的砖块</li>
                                </ul>
        
                                <h4>2. 物理系统实现：</h4>
                                <ul>
                                    <li><strong>精确的球体物理：</strong>
                                        <ul>
                                            <li>基于角度和速度的真实反弹模型</li>
                                            <li>挡板碰撞位置影响反弹角度（边缘产生更大角度）</li>
                                            <li>碰撞时有适当的速度变化和加速效果</li>
                                        </ul>
                                    </li>
                                    <li><strong>多样化的砖块与碰撞：</strong>
                                        <ul>
                                            <li>精确判断球体撞击砖块的哪一面并给予相应反弹</li>
                                            <li>不同类型砖块：普通砖块、坚固砖块（需多次撞击）、特殊砖块（掉落道具）</li>
                                            <li>球与环境边界的碰撞检测和反应</li>
                                        </ul>
                                    </li>
                                </ul>
        
                                <h4>3. AI自动演示系统：</h4>
                                <ul>
                                    <li><strong>智能AI玩家：</strong>
                                        <ul>
                                            <li>实现预测算法，计算球的落点并移动挡板接球</li>
                                            <li>AI决策系统：根据当前球速和方向做出最佳挡板位置选择</li>
                                            <li>添加适度的"人类化"反应：偶尔犯错、有反应延迟、预判不完美</li>
                                        </ul>
                                    </li>
                                    <li><strong>AI模式交互：</strong>
                                        <ul>
                                            <li>游戏启动时先进入AI自动演示模式，展示游戏玩法</li>
                                            <li>提供明显的按钮切换AI和人类玩家模式</li>
                                            <li>AI模式下显示实时决策过程，如预测轨迹线或目标位置标记</li>
                                        </ul>
                                    </li>
                                </ul>
        
                                <h4>4. 游戏内容与机制：</h4>
                                <ul>
                                    <li><strong>砖块系统：</strong>多种类型砖块，至少3个不同布局的关卡</li>
                                    <li><strong>道具系统：</strong>4-5种不同效果的道具（多球、扩展挡板、减速、穿透等）</li>
                                    <li><strong>游戏状态：</strong>3条生命，分数系统，关卡进度</li>
                                </ul>
        
                                <h4>5. 8位风格音效系统（使用Web Audio API）：</h4>
                                <ul>
                                    <li>必须使用Web Audio API编程生成所有音效，不使用外部音频文件</li>
                                    <li>球体碰撞音效、砖块破坏音效、道具获取音效等</li>
                                    <li>使用方波、三角波等音源创建复古音色</li>
                                    <li>循环背景音乐，符合8位游戏风格</li>
                                </ul>
        
                                <h4>6. UI与控制：</h4>
                                <ul>
                                    <li>显示分数、最高分、剩余生命和关卡信息</li>
                                    <li>键盘和鼠标/触摸支持</li>
                                    <li>明确的AI/人类模式切换按钮</li>
                                </ul>
        
                                <h4>7. 代码质量要求：</h4>
                                <ul>
                                    <li>代码组织良好，使用模块化结构</li>
                                    <li>详细注释，解释声音合成原理和AI决策算法</li>
                                    <li>优化性能，确保游戏和音效播放流畅</li>
                                </ul>
        
                                <p>请实现完整的单文件HTML游戏，确保所有音效都通过Web Audio API动态生成，不依赖任何外部资源。游戏应自动启动进入AI演示模式，展示物理系统和玩法，并允许玩家随时接管控制。
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </div>

        <div class="projects-grid">
<!-- 太阳系运行图项目 -->
            <div class="project-section">
                <h2 class="project-title">🌌 太阳系运行图</h2>
                <p class="project-description">Three.js驱动的3D太阳系模拟器，展现宇宙之美。对比不同AI模型在3D图形编程方面的实现方式。</p>
                
                <div class="models-grid">
                    <div class="model-card model-claude4">
                        <div class="model-header">
                            <div class="model-name">Claude 4.0 (3D)</div>
                            <div class="model-info">
                                <span>高级3D版本</span>
                                <span class="file-size">21KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">599</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">高级</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">Three.js</span>
                                <span class="feature-tag">控制面板</span>
                                <span class="feature-tag">速度调节</span>
                                <span class="feature-tag">视觉特效</span>
                            </div>
                            <a href="solar_system_3d_claude4.html" class="preview-btn" target="_blank">
                                🚀 体验 Claude 4.0 3D版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-claude3">
                        <div class="model-header">
                            <div class="model-name">Claude 3.7</div>
                            <div class="model-info">
                                <span>稳定版本</span>
                                <span class="file-size">15KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">395</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">中等</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">轨道控制</span>
                                <span class="feature-tag">平滑动画</span>
                                <span class="feature-tag">稳定性优</span>
                            </div>
                            <a href="solar_system-claude3.7.html" class="preview-btn" target="_blank">
                                ⭐ 体验 Claude 3.7 版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-kimi">
                        <div class="model-header">
                            <div class="model-name">Kimi 2.0 (3D)</div>
                            <div class="model-info">
                                <span>简约3D版本</span>
                                <span class="file-size">7.1KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">224</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">简单</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">轻量级</span>
                                <span class="feature-tag">快速加载</span>
                                <span class="feature-tag">简洁界面</span>
                            </div>
                            <a href="solar_system_3d_kimi2.html" class="preview-btn" target="_blank">
                                🎯 体验 Kimi 2.0 3D版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-deepseek">
                        <div class="model-header">
                            <div class="model-name">DeepSeek V3</div>
                            <div class="model-info">
                                <span>动态模拟器</span>
                                <span class="file-size">15KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">432</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">中高</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">动态效果</span>
                                <span class="feature-tag">交互控制</span>
                                <span class="feature-tag">物理模拟</span>
                            </div>
                            <a href="solar_system-deepseekv3.html" class="preview-btn" target="_blank">
                                💎 体验 DeepSeek V3 版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-chatgpt">
                        <div class="model-header">
                            <div class="model-name">ChatGPT</div>
                            <div class="model-info">
                                <span>经典实现</span>
                                <span class="file-size">9.5KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">241</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">基础</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">基础功能</span>
                                <span class="feature-tag">易于理解</span>
                                <span class="feature-tag">入门友好</span>
                            </div>
                            <a href="solar_system-chatgpt.html" class="preview-btn" target="_blank">
                                🌟 体验 ChatGPT 版本
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 像素弹球游戏项目 -->
            <div class="project-section">
                <h2 class="project-title">🎮 像素弹球大师</h2>
                <p class="project-description">经典的弹球游戏，采用像素风格设计。体验不同AI模型在游戏开发方面的创意实现。</p>
                
                <div class="models-grid">
                    <div class="model-card model-claude4">
                        <div class="model-header">
                            <div class="model-name">Claude 4.0</div>
                            <div class="model-info">
                                <span>最新版本</span>
                                <span class="file-size">39KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">1146</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">高级</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">像素风格</span>
                                <span class="feature-tag">游戏统计</span>
                                <span class="feature-tag">响应式设计</span>
                                <span class="feature-tag">特效丰富</span>
                            </div>
                            <a href="pixel_breakout-claude4.html" class="preview-btn" target="_blank">
                                🚀 体验 Claude 4.0 版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-claude3">
                        <div class="model-header">
                            <div class="model-name">Claude 3.5</div>
                            <div class="model-info">
                                <span>经典版本</span>
                                <span class="file-size">16KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">543</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">中等</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">简洁设计</span>
                                <span class="feature-tag">流畅动画</span>
                                <span class="feature-tag">易于理解</span>
                            </div>
                            <a href="pixel_breakout-claude3.5.html" class="preview-btn" target="_blank">
                                ⭐ 体验 Claude 3.5 版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-kimi">
                        <div class="model-header">
                            <div class="model-name">Kimi 2.0</div>
                            <div class="model-info">
                                <span>红白机风格</span>
                                <span class="file-size">22KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">722</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">中高</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">怀旧风格</span>
                                <span class="feature-tag">像素字体</span>
                                <span class="feature-tag">经典配色</span>
                            </div>
                            <a href="pixel_breakout-kimi2.html" class="preview-btn" target="_blank">
                                🎯 体验 Kimi 2.0 版本
                            </a>
                        </div>
                    </div>

                    <div class="model-card model-deepseek">
                        <div class="model-header">
                            <div class="model-name">DeepSeek V3</div>
                            <div class="model-info">
                                <span>功能丰富</span>
                                <span class="file-size">55KB</span>
                            </div>
                        </div>
                        <div class="model-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <span class="stat-value">1565</span>
                                    <span>代码行数</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value">最高</span>
                                    <span>复杂度</span>
                                </div>
                            </div>
                            <div class="features-list">
                                <span class="feature-tag">功能完整</span>
                                <span class="feature-tag">触控支持</span>
                                <span class="feature-tag">高级特效</span>
                                <span class="feature-tag">优化性能</span>
                            </div>
                            <a href="pixel_breakout-deepseekv3.html" class="preview-btn" target="_blank">
                                💎 体验 DeepSeek V3 版本
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            
        </div>

        

        <div class="comparison-note">
            <h3>🔍 对比观察要点</h3>
            <p>
                每个AI模型都有其独特的编程风格和实现方式。Claude 4.0通常提供更丰富的功能和视觉效果，
                Claude 3.5注重代码的简洁性和可读性，Kimi擅长创造具有特色风格的界面，
                DeepSeek V3倾向于实现完整和复杂的功能，而ChatGPT则提供基础但稳定的实现。
                通过对比这些版本，您可以深入了解不同AI模型在创意编程方面的能力差异。
            </p>
        </div>
    </div>

    <script>
        // 添加一些交互动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.model-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html> 