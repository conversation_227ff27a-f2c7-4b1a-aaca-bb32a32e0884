<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太阳系运行图</title>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #000;
            color: white;
        }

        canvas {
            display: block;
        }

        #info {
            position: absolute;
            top: 10px;
            width: 100%;
            text-align: center;
            color: white;
            pointer-events: none;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
        }

        #controls button {
            margin: 5px;
            padding: 8px 12px;
            background-color: #2a3b90;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        #controls button:hover {
            background-color: #3a4da0;
        }

        #controls button.active {
            background-color: #4a5db0;
        }

        .label {
            color: #ffffff;
            font-size: 10px;
            font-weight: bold;
            background-color: transparent;
            padding: 2px;
            white-space: nowrap;
        }

        #showLabels,
        #showOrbits {
            margin-left: 10px;
        }
    </style>
</head>

<body>
    <div id="info">太阳系运行图</div>
    <div id="controls">
        <button id="slowBtn">慢速</button>
        <button id="normalBtn" class="active">中速</button>
        <button id="fastBtn">快速</button>
        <label><input type="checkbox" id="showLabels" checked> 显示行星名称</label>
        <label><input type="checkbox" id="showOrbits" checked> 显示轨道</label>
    </div>

    <script>
        // 场景初始化
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000);
        document.body.appendChild(renderer.domElement);

        // 轨道控制
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        camera.position.set(0, 50, 100);
        controls.update();

        // 添加环境光和平行光
        const ambientLight = new THREE.AmbientLight(0x333333);
        scene.add(ambientLight);

        // 星星背景
        function createStars() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 0.7,
                sizeAttenuation: false
            });

            const starsVertices = [];
            for (let i = 0; i < 5000; i++) {
                const x = THREE.MathUtils.randFloatSpread(2000);
                const y = THREE.MathUtils.randFloatSpread(2000);
                const z = THREE.MathUtils.randFloatSpread(2000);
                starsVertices.push(x, y, z);
            }

            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const stars = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(stars);
        }

        createStars();

        // 太阳系参数（半径按比例缩小，轨道半径也按比例调整）
        const planetData = [
            { name: "太阳", radius: 5, distance: 0, color: 0xffff00, rotationSpeed: 0.002, orbitalSpeed: 0, hasRings: false, tilt: 0 },
            { name: "水星", radius: 0.5, distance: 10, color: 0xbebebe, rotationSpeed: 0.004, orbitalSpeed: 0.04, hasRings: false, tilt: 0.034 },
            { name: "金星", radius: 0.9, distance: 15, color: 0xffd700, rotationSpeed: 0.002, orbitalSpeed: 0.015, hasRings: false, tilt: 2.64 },
            { name: "地球", radius: 1, distance: 20, color: 0x0077ff, rotationSpeed: 0.01, orbitalSpeed: 0.01, hasRings: false, tilt: 0.41 },
            { name: "火星", radius: 0.6, distance: 25, color: 0xff4500, rotationSpeed: 0.008, orbitalSpeed: 0.008, hasRings: false, tilt: 0.44 },
            { name: "木星", radius: 2.5, distance: 40, color: 0xffa500, rotationSpeed: 0.04, orbitalSpeed: 0.002, hasRings: false, tilt: 0.05 },
            { name: "土星", radius: 2.2, distance: 55, color: 0xf0c070, rotationSpeed: 0.038, orbitalSpeed: 0.0009, hasRings: true, tilt: 0.47 },
            { name: "天王星", radius: 1.8, distance: 70, color: 0x40ffff, rotationSpeed: 0.03, orbitalSpeed: 0.0004, hasRings: false, tilt: 1.71 },
            { name: "海王星", radius: 1.7, distance: 85, color: 0x0000ff, rotationSpeed: 0.032, orbitalSpeed: 0.0001, hasRings: false, tilt: 0.49 },
            { name: "冥王星", radius: 0.3, distance: 100, color: 0x8b7d82, rotationSpeed: 0.008, orbitalSpeed: 0.00007, hasRings: false, tilt: 1.0 }
        ];

        const planets = [];
        const orbits = [];
        const planetLabels = [];

        // 创建太阳（发光效果）
        function createSun() {
            const sunGeometry = new THREE.SphereGeometry(planetData[0].radius, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({ color: planetData[0].color });
            const sun = new THREE.Mesh(sunGeometry, sunMaterial);

            // 太阳光源
            const sunLight = new THREE.PointLight(0xffffff, 1.5, 1000);
            sun.add(sunLight);

            // 太阳光晕
            const sunGlowGeometry = new THREE.SphereGeometry(planetData[0].radius * 1.2, 32, 32);
            const sunGlowMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    viewVector: { type: "v3", value: camera.position }
                },
                vertexShader: `
                    uniform vec3 viewVector;
                    varying float intensity;
                    void main() {
                        vec3 vNormal = normalize(normalMatrix * normal);
                        vec3 vNormel = normalize(normalMatrix * viewVector);
                        intensity = pow(0.65 - dot(vNormal, vNormel), 4.0);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    varying float intensity;
                    void main() {
                        vec3 glow = vec3(1.0, 0.8, 0.0) * intensity;
                        gl_FragColor = vec4(glow, 1.0);
                    }
                `,
                side: THREE.BackSide,
                blending: THREE.AdditiveBlending,
                transparent: true
            });

            const sunGlow = new THREE.Mesh(sunGlowGeometry, sunGlowMaterial);
            scene.add(sunGlow);
            scene.add(sun);
            planets.push({ mesh: sun, data: planetData[0], glow: sunGlow });

            return sun;
        }

        // 创建行星
        function createPlanet(data, index) {
            const planetGeometry = new THREE.SphereGeometry(data.radius, 32, 32);
            const planetMaterial = new THREE.MeshLambertMaterial({ color: data.color });
            const planet = new THREE.Mesh(planetGeometry, planetMaterial);

            // 行星轨道组
            const orbitGroup = new THREE.Group();
            scene.add(orbitGroup);

            // 添加轨道可视化
            const orbitGeometry = new THREE.RingGeometry(data.distance - 0.1, data.distance + 0.1, 128);
            const orbitMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.3
            });
            const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
            orbit.rotation.x = Math.PI / 2;
            scene.add(orbit);
            orbits.push(orbit);

            // 设置行星初始位置
            planet.position.x = data.distance;
            orbitGroup.add(planet);

            // 添加土星环
            if (data.hasRings) {
                const ringGeometry = new THREE.RingGeometry(data.radius * 1.4, data.radius * 2.2, 32);
                const ringMaterial = new THREE.MeshBasicMaterial({
                    color: 0xc9a880,
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.8
                });
                const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                ring.rotation.x = Math.PI / 2;
                planet.add(ring);
            }

            // 添加地球的月球
            if (data.name === "地球") {
                const moonGeometry = new THREE.SphereGeometry(0.3, 32, 32);
                const moonMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
                const moon = new THREE.Mesh(moonGeometry, moonMaterial);
                moon.position.x = 2;
                planet.add(moon);
            }

            // 添加行星标签
            const planetDiv = document.createElement('div');
            planetDiv.className = 'label';
            planetDiv.textContent = data.name;

            const planetLabel = new CSS2DRenderer(planetDiv);
            planetLabel.position.copy(planet.position);
            planetLabel.position.y = data.radius + 2;
            planet.add(planetLabel);
            planetLabels.push(planetLabel);

            planets.push({ mesh: planet, orbitGroup: orbitGroup, data: data, moon: data.name === "地球" ? planet.children[0] : null });

            return orbitGroup;
        }

        // CSS2D渲染器模拟
        function CSS2DRenderer(element) {
            const div = document.createElement('div');
            div.style.position = 'absolute';
            div.style.pointerEvents = 'none';
            div.appendChild(element);
            document.body.appendChild(div);

            this.element = div;
            this.position = new THREE.Vector3();

            this.updatePosition = function (camera) {
                if (this.position) {
                    const vector = this.position.clone().project(camera);
                    vector.x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                    vector.y = -(vector.y * 0.5 - 0.5) * window.innerHeight;

                    this.element.style.left = vector.x + 'px';
                    this.element.style.top = vector.y + 'px';
                }
            };
        }

        // 创建太阳系
        const sun = createSun();
        for (let i = 1; i < planetData.length; i++) {
            createPlanet(planetData[i], i);
        }

        // 动画速度
        let speedMultiplier = 1;
        let showLabelsEnabled = true;
        let showOrbitsEnabled = true;

        // 太阳脉动效果参数
        let pulseFactor = 0;
        const pulseSpeed = 0.03;
        const pulseAmount = 0.05;

        // 控制按钮事件监听
        document.getElementById('slowBtn').addEventListener('click', function () {
            speedMultiplier = 0.5;
            updateButtonState(this);
        });

        document.getElementById('normalBtn').addEventListener('click', function () {
            speedMultiplier = 1;
            updateButtonState(this);
        });

        document.getElementById('fastBtn').addEventListener('click', function () {
            speedMultiplier = 3;
            updateButtonState(this);
        });

        document.getElementById('showLabels').addEventListener('change', function () {
            showLabelsEnabled = this.checked;
            planetLabels.forEach(label => {
                label.element.style.display = showLabelsEnabled ? 'block' : 'none';
            });
        });

        document.getElementById('showOrbits').addEventListener('change', function () {
            showOrbitsEnabled = this.checked;
            orbits.forEach(orbit => {
                orbit.visible = showOrbitsEnabled;
            });
        });

        function updateButtonState(activeBtn) {
            document.querySelectorAll('#controls button').forEach(btn => {
                btn.classList.remove('active');
            });
            activeBtn.classList.add('active');
        }

        // 处理窗口大小调整
        window.addEventListener('resize', function () {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 太阳脉动效果
            pulseFactor += pulseSpeed;
            const scale = 1 + Math.sin(pulseFactor) * pulseAmount;
            planets[0].mesh.scale.set(scale, scale, scale);
            planets[0].glow.scale.set(scale * 1.2, scale * 1.2, scale * 1.2);

            // 行星自转和公转
            planets.forEach((planet, index) => {
                if (index > 0) {  // 跳过太阳
                    // 行星自转
                    planet.mesh.rotation.y += planet.data.rotationSpeed * speedMultiplier;

                    // 行星公转
                    planet.orbitGroup.rotation.y += planet.data.orbitalSpeed * speedMultiplier;

                    // 如果是地球，更新月球位置
                    if (planet.moon) {
                        planet.moon.rotation.y += 0.02 * speedMultiplier;
                    }
                }
            });

            // 更新标签位置
            if (showLabelsEnabled) {
                planets.forEach((planet, index) => {
                    const worldPos = new THREE.Vector3();
                    if (index === 0) {
                        planet.mesh.getWorldPosition(worldPos);
                        planetLabels[index] ? planetLabels[index].position.copy(new THREE.Vector3(worldPos.x, worldPos.y + planet.data.radius + 2, worldPos.z)) : null;
                    } else {
                        planet.mesh.getWorldPosition(worldPos);
                        planetLabels[index] ? planetLabels[index].position.copy(new THREE.Vector3(worldPos.x, worldPos.y + planet.data.radius + 1, worldPos.z)) : null;
                    }
                });

                planetLabels.forEach(label => {
                    label.updatePosition(camera);
                });
            }

            // 更新太阳光晕效果
            planets[0].glow.material.uniforms.viewVector.value = new THREE.Vector3().subVectors(camera.position, planets[0].mesh.position);

            controls.update();
            renderer.render(scene, camera);
        }

        animate();
    </script>
</body>

</html>