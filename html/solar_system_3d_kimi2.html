<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>Three.js 太阳系运行图</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: #000;
      font-family: Arial, Helvetica, sans-serif;
    }

    #container {
      width: 100vw;
      height: 100vh;
    }

    #controls {
      position: absolute;
      top: 10px;
      left: 10px;
      color: #fff;
      z-index: 100;
      display: flex;
      gap: 6px;
    }

    button {
      padding: 6px 14px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.25);
      color: #fff;
      transition: background 0.3s;
    }

    button:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  </style>
</head>

<body>
  <div id="container"></div>

  <!-- 速度控制 -->
  <div id="controls">
    <button id="slow">慢速</button>
    <button id="normal">中速</button>
    <button id="fast">快速</button>
  </div>

  <!-- 让浏览器认识 import -->
  <script type="importmap">
    {
        "imports": {
            "three": "https://cdn.jsdelivr.net/npm/three@0.159.0/build/three.module.js",
            "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.159.0/examples/jsm/"
        }
    }
    </script>

  <script type="module">
    import * as THREE from 'three';
    import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

    /* ========== 基础设置 ========== */
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(60, innerWidth / innerHeight, 0.1, 10000);
    camera.position.set(0, 120, 300);

    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(innerWidth, innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    document.getElementById('container').appendChild(renderer.domElement);

    const controls = new OrbitControls(camera, renderer.domElement);

    /* ========== 星空背景 ========== */
    const starCount = 5000;
    const starGeo = new THREE.BufferGeometry();
    const starPos = [];
    for (let i = 0; i < starCount; i++) {
      starPos.push(
        THREE.MathUtils.randFloatSpread(2000),
        THREE.MathUtils.randFloatSpread(2000),
        THREE.MathUtils.randFloatSpread(2000)
      );
    }
    starGeo.setAttribute('position', new THREE.Float32BufferAttribute(starPos, 3));
    scene.add(new THREE.Points(starGeo, new THREE.PointsMaterial({ color: 0xffffff, size: 0.8 })));

    /* ========== 行星数据 ========== */
    const planetData = [
      { name: '水星', radius: 2.4, distance: 40, color: 0xc2b280, speed: 4.74 },
      { name: '金星', radius: 6.1, distance: 60, color: 0xe6c229, speed: 3.5 },
      { name: '地球', radius: 6.4, distance: 85, color: 0x2b82c9, speed: 2.98, moon: true },
      { name: '火星', radius: 3.4, distance: 110, color: 0xc1440e, speed: 2.41 },
      { name: '木星', radius: 14, distance: 160, color: 0xd8ca9d, speed: 1.31 },
      { name: '土星', radius: 12, distance: 220, color: 0xe3c078, speed: 0.97, ring: true },
      { name: '天王星', radius: 10, distance: 280, color: 0x5ddef4, speed: 0.68 },
      { name: '海王星', radius: 10, distance: 330, color: 0x3454eb, speed: 0.54 },
      { name: '冥王星', radius: 1.2, distance: 380, color: 0xaaaaaa, speed: 0.47 }
    ];

    /* ========== 太阳 ========== */
    const sunMesh = new THREE.Mesh(
      new THREE.SphereGeometry(18, 32, 32),
      new THREE.MeshBasicMaterial({ color: 0xffdd00 })
    );
    scene.add(sunMesh);
    /* 太阳发光 */
      const sunLight = new THREE.PointLight(0xffffff, 2.5, 0);
      sunLight.position.set(0, 0, 0);
      scene.add(sunLight);

      /* 补一点环境光，防止背光面全黑 */
      scene.add(new THREE.AmbientLight(0x404040));
      // scene.add(new THREE.PointLight(0xffffff, 2, 0));
      // scene.add(new THREE.AmbientLight(0x222222));

    /* ========== 创建行星 ========== */
    const planets = [];
    planetData.forEach(p => {
      /* 轨道线 */
      const trackMesh = new THREE.Mesh(
        new THREE.RingGeometry(p.distance, p.distance + 0.1, 128),
        new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.2, side: THREE.DoubleSide })
      );
      trackMesh.rotation.x = -Math.PI / 2;
      scene.add(trackMesh);

      /* 行星组 */
      const planetGroup = new THREE.Group();
      scene.add(planetGroup);

      /* 行星本体 */
      const planetMesh = new THREE.Mesh(
        new THREE.SphereGeometry(p.radius, 32, 32),
        new THREE.MeshStandardMaterial({ color: p.color })
      );
      planetMesh.position.x = p.distance;
      planetGroup.add(planetMesh);

      /* 标签 */
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = 256; canvas.height = 128;
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(p.name, 128, 64);
      const sprite = new THREE.Sprite(new THREE.SpriteMaterial({ map: new THREE.CanvasTexture(canvas) }));
      sprite.scale.set(20, 10, 1);
      sprite.position.set(p.distance, p.radius + 10, 0);
      planetGroup.add(sprite);

      /* 土星环 */
      if (p.ring) {
        const ringMesh = new THREE.Mesh(
          new THREE.RingGeometry(p.radius * 1.4, p.radius * 2.2, 64),
          new THREE.MeshBasicMaterial({ color: 0xc2b280, side: THREE.DoubleSide, transparent: true, opacity: 0.6 })
        );
        ringMesh.rotation.x = -Math.PI / 3;
        planetMesh.add(ringMesh);
      }

      /* 月球 */
      if (p.moon) {
        const moonGroup = new THREE.Group();
        planetMesh.add(moonGroup);
        const moonMesh = new THREE.Mesh(
          new THREE.SphereGeometry(1.6, 16, 16),
          new THREE.MeshStandardMaterial({ color: 0xcccccc })
        );
        moonMesh.position.x = 15;
        moonGroup.add(moonMesh);
        planets.push({ group: moonGroup, speed: 10, dist: 15, isMoon: true });
      }

      planets.push({ group: planetGroup, speed: p.speed, mesh: planetMesh });
    });

    /* ========== 速度控制 ========== */
    let speedFactor = 1;
    document.getElementById('slow').onclick = () => speedFactor = 0.2;
    document.getElementById('normal').onclick = () => speedFactor = 1;
    document.getElementById('fast').onclick = () => speedFactor = 5;

    /* ========== 动画循环 ========== */
    function animate() {
      requestAnimationFrame(animate);

      /* 太阳脉动 */
      const pulse = Math.sin(Date.now() * 0.002) * 0.05 + 1;
      sunMesh.scale.set(pulse, pulse, pulse);

      /* 行星公转 */
      planets.forEach(p => {
        const angle = Date.now() * 0.0001 * speedFactor * p.speed;
        if (p.isMoon) {
          p.group.rotation.y = angle;
        } else {
          p.group.rotation.y = angle;
        }
      });

      renderer.render(scene, camera);
    }
    animate();

    /* ========== 响应式 ========== */
    window.addEventListener('resize', () => {
      camera.aspect = innerWidth / innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(innerWidth, innerHeight);
    });
  </script>
</body>

</html>